#!/usr/bin/env python3
"""
Start the web API server with proper environment variable loading
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

print("=== Starting Web API Server ===")
print(f"ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
print(f"LLM_PROVIDER: {os.getenv('LLM_PROVIDER')}")
print(f"USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM')}")
print("================================")

# Import and run the web API
import uvicorn
import web_api

if __name__ == "__main__":
    web_api.initialize_app()
    
    # Run server
    uvicorn.run(
        "web_api:app",
        host="0.0.0.0",
        port=8006,
        reload=True,
        log_level="info"
    )