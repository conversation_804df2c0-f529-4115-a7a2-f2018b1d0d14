"""
Example usage of the Document Analysis System.

This script demonstrates how to use the Python implementation
for document analysis and prompt testing.
"""

import sys
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import Config
from src.core.analyzer import Document<PERSON>nalyzer
from src.core.prompts import PromptTemplates
from src.utils.document_reader import DocumentReader, create_sample_document
from src.testing.prompt_tester import PromptTester


def main():
    parser = argparse.ArgumentParser(description="Document Analysis System Examples")
    parser.add_argument("--example", type=str, choices=[
        "basic_analysis", "prompt_testing", "batch_analysis", "custom_config"
    ], help="Example to run")
    parser.add_argument("--document", type=str, help="Document file to analyze")
    parser.add_argument("--directory", type=str, help="Directory of documents to analyze")
    parser.add_argument("--config", type=str, help="Configuration file path")
    parser.add_argument("--output", type=str, help="Output directory")
    parser.add_argument("--mock", action="store_true", help="Use mock LLM for testing")
    
    args = parser.parse_args()
    
    if args.example == "basic_analysis":
        run_basic_analysis(args)
    elif args.example == "prompt_testing":
        run_prompt_testing(args)
    elif args.example == "batch_analysis":
        run_batch_analysis(args)
    elif args.example == "custom_config":
        run_custom_config_example(args)
    else:
        print("Please specify an example with --example")
        print("Available examples: basic_analysis, prompt_testing, batch_analysis, custom_config")


def run_basic_analysis(args):
    """Run basic document analysis example."""
    print("=== Basic Document Analysis Example ===")
    
    # Load or create configuration
    if args.config:
        config = Config.from_file(args.config)
    else:
        config = Config()
        config.use_mock_llm = args.mock
    
    if args.output:
        config.output_dir = args.output
    
    # Get document
    if args.document:
        reader = DocumentReader()
        document = reader.read_file(args.document)
    else:
        document = create_sample_document()
    
    print(f"Analyzing document: {document.doc_id}")
    print(f"Document length: {len(document.text)} characters")
    
    # Create analyzer
    analyzer = DocumentAnalyzer(config)
    
    # Analyze document
    result = analyzer.analyze_document(document, "unified_analysis")
    
    # Display results
    print(f"\\nAnalysis Results:")
    print(f"- Quality Score: {result.quality_score:.2f}")
    print(f"- Execution Time: {result.execution_time:.2f}s")
    print(f"- Warnings: {len(result.warnings)}")
    
    if result.analysis:
        actors = result.analysis.get('actors', [])
        print(f"- Actors Found: {len(actors)}")
        
        relationships = result.analysis.get('relationships', {})
        rel_items = relationships.get('items', [])
        print(f"- Relationships Found: {len(rel_items)}")
        
        portrayals = result.analysis.get('portrayals', {})
        port_items = portrayals.get('items', [])
        print(f"- Portrayals Found: {len(port_items)}")
    
    # Save result
    output_file = analyzer.save_result(result)
    print(f"\\nResults saved to: {output_file}")


def run_prompt_testing(args):
    """Run prompt testing example."""
    print("=== Prompt Testing Example ===")
    
    # Load or create configuration
    if args.config:
        config = Config.from_file(args.config)
    else:
        config = Config()
        config.use_mock_llm = args.mock
    
    if args.output:
        config.output_dir = args.output
    
    # Get documents
    documents = []
    if args.document:
        reader = DocumentReader()
        documents.append(reader.read_file(args.document))
    elif args.directory:
        reader = DocumentReader()
        documents = reader.read_directory(args.directory)
    else:
        documents = [create_sample_document()]
    
    print(f"Testing with {len(documents)} documents")
    
    # Create tester
    tester = PromptTester(config)
    
    # Test all templates
    all_results = []
    for doc in documents:
        print(f"\\nTesting with document: {doc.doc_id}")
        results = tester.test_all_templates(doc)
        all_results.extend(results)
        
        # Show quick results
        for result in results:
            status = "OK" if result.parse_success else "FAIL"
            print(f"  {status} {result.template_name}: {result.execution_time:.2f}s, "
                  f"Quality: {result.quality_score:.2f}")
    
    # Generate report
    report_dir = tester.generate_report()
    print(f"\\nTest report generated in: {report_dir}")
    
    # Show summary
    metrics = tester.calculate_metrics()
    print(f"\\nSummary:")
    print(f"- Total tests: {metrics.total_tests}")
    print(f"- Successful tests: {metrics.successful_tests}")
    print(f"- Success rate: {metrics.parse_success_rate:.2%}")
    print(f"- Average quality score: {metrics.average_quality_score:.2f}")


def run_batch_analysis(args):
    """Run batch analysis example."""
    print("=== Batch Document Analysis Example ===")
    
    # Load or create configuration
    if args.config:
        config = Config.from_file(args.config)
    else:
        config = Config()
        config.use_mock_llm = args.mock
    
    if args.output:
        config.output_dir = args.output
    
    # Get documents
    if args.directory:
        reader = DocumentReader()
        documents = reader.read_directory(args.directory)
    elif args.document:
        reader = DocumentReader()
        documents = [reader.read_file(args.document)]
    else:
        # Create sample documents
        documents = [
            create_sample_document(),
            create_sample_document(),
            create_sample_document()
        ]
        # Modify them slightly
        documents[1].doc_id = "sample_002"
        documents[1].title = "Sample Document 2"
        documents[2].doc_id = "sample_003"
        documents[2].title = "Sample Document 3"
    
    print(f"Analyzing {len(documents)} documents")
    
    # Create analyzer
    analyzer = DocumentAnalyzer(config)
    
    # Analyze documents
    results = analyzer.analyze_documents_batch(documents)
    
    # Display results
    successful_results = [r for r in results if r.analysis]
    failed_results = [r for r in results if not r.analysis]
    
    print(f"\\nBatch Analysis Results:")
    print(f"- Total documents: {len(documents)}")
    print(f"- Successful analyses: {len(successful_results)}")
    print(f"- Failed analyses: {len(failed_results)}")
    
    if successful_results:
        avg_quality = sum(r.quality_score for r in successful_results) / len(successful_results)
        avg_time = sum(r.execution_time for r in successful_results) / len(successful_results)
        print(f"- Average quality score: {avg_quality:.2f}")
        print(f"- Average execution time: {avg_time:.2f}s")
    
    # Show individual results
    print("\\nIndividual Results:")
    for result in results:
        status = "OK" if result.analysis else "FAIL"
        print(f"  {status} {result.doc_id}: {result.execution_time:.2f}s, "
              f"Quality: {result.quality_score:.2f}")
    
    # Save batch results
    output_file = analyzer.save_batch_results(results)
    print(f"\\nBatch results saved to: {output_file}")
    
    # Show summary
    summary = analyzer.get_analysis_summary()
    print("\\nAnalysis Summary:")
    for key, value in summary.items():
        print(f"- {key}: {value}")


def run_custom_config_example(args):
    """Run custom configuration example."""
    print("=== Custom Configuration Example ===")
    
    # Create custom configuration
    config = Config()
    
    # Modify configuration
    config.llm.provider = "openai"
    config.llm.model = "gpt-4"
    config.llm.temperature = 0.1
    config.llm.max_tokens = 2000
    
    config.analysis.min_confidence_score = 0.8
    config.analysis.max_document_length = 5000
    config.analysis.enable_quality_assessment = True
    
    config.output.include_confidence_scores = True
    config.output.pretty_print = True
    
    config.use_mock_llm = args.mock
    
    if args.output:
        config.output_dir = args.output
    
    # Save configuration
    config_file = "custom_config.json"
    config.save_to_file(config_file)
    print(f"Custom configuration saved to: {config_file}")
    
    # Show configuration
    print("\\nCustom Configuration:")
    print(f"LLM Provider: {config.llm.provider}")
    print(f"LLM Model: {config.llm.model}")
    print(f"Temperature: {config.llm.temperature}")
    print(f"Max Tokens: {config.llm.max_tokens}")
    print(f"Min Confidence Score: {config.analysis.min_confidence_score}")
    print(f"Max Document Length: {config.analysis.max_document_length}")
    
    # Validate configuration
    errors = config.validate()
    if errors:
        print("\\nConfiguration Errors:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\\nConfiguration is valid!")
    
    # Test with sample document
    document = create_sample_document()
    analyzer = DocumentAnalyzer(config)
    result = analyzer.analyze_document(document)
    
    print(f"\\nTest Analysis:")
    print(f"- Quality Score: {result.quality_score:.2f}")
    print(f"- Execution Time: {result.execution_time:.2f}s")
    print(f"- Warnings: {len(result.warnings)}")


if __name__ == "__main__":
    main()