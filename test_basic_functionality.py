#!/usr/bin/env python3
"""
Test basic functionality without hanging issues.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    try:
        from src.core.config import Config
        from src.core.analyzer import DocumentAnalyzer
        from src.utils.document_reader import create_sample_document
        print("✓ Basic imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_mock_analysis():
    """Test mock analysis."""
    print("Testing mock analysis...")
    try:
        from src.core.config import Config
        from src.core.analyzer import DocumentAnalyzer
        from src.utils.document_reader import create_sample_document
        
        # Create config with mock LLM
        config = Config()
        config.use_mock_llm = True
        
        # Create analyzer
        analyzer = DocumentAnalyzer(config)
        
        # Create sample document
        document = create_sample_document()
        
        # Analyze document
        result = analyzer.analyze_document(document, "unified_analysis")
        
        print(f"✓ Analysis completed - Quality: {result.quality_score:.2f}")
        print(f"  Actors: {len(result.analysis.get('actors', []))}")
        print(f"  Execution time: {result.execution_time:.2f}s")
        
        return True
    except Exception as e:
        print(f"✗ Mock analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modular_analysis():
    """Test modular analysis."""
    print("Testing modular analysis...")
    try:
        from src.core.config import Config
        from src.core.modular_analyzer import ModularDocumentAnalyzer
        from src.core.modular_prompts import AnalysisMode
        from src.utils.document_reader import create_sample_document
        
        # Create config with mock LLM
        config = Config()
        config.use_mock_llm = True
        
        # Create modular analyzer
        analyzer = ModularDocumentAnalyzer(config)
        
        # Create sample document
        document = create_sample_document()
        
        # Analyze document
        result = analyzer.analyze_modular(
            document,
            mode=AnalysisMode.UNIFIED
        )
        
        print(f"✓ Modular analysis completed - Quality: {result.quality_score:.2f}")
        print(f"  Mode: {result.mode.value}")
        print(f"  Task timings: {len(result.task_timings)}")
        
        return True
    except Exception as e:
        print(f"✗ Modular analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting basic functionality tests...")
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Mock Analysis", test_mock_analysis),
        ("Modular Analysis", test_modular_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n--- Summary ---")
    for test_name, result in results:
        print(f"{test_name}: {'PASS' if result else 'FAIL'}")
    
    passed = sum(1 for _, result in results if result)
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("✓ All basic functionality tests passed!")
    else:
        print("✗ Some tests failed - check the output above")
