#!/usr/bin/env python3
"""
Simple debug script to test basic functionality.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("Starting simple debug test...")

try:
    print("1. Testing environment variables...")
    print(f"   USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM')}")
    print(f"   LLM_PROVIDER: {os.getenv('LLM_PROVIDER')}")
    print(f"   ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
    
    print("2. Testing config import...")
    from src.core.config import Config, LLMConfig
    print("   ✓ Config import OK")
    
    print("3. Testing config creation...")
    config = Config()
    print(f"   ✓ Config created - provider: {config.llm.provider}")
    
    print("4. Testing mock LLM client...")
    from src.core.llm_client import MockLLMClient
    mock_client = MockLLMClient(LLMConfig())
    print("   ✓ Mock client created")
    
    print("5. Testing mock response...")
    response = mock_client.generate_response("Test prompt")
    print(f"   ✓ Mock response generated (length: {len(response)})")
    
    print("6. Testing create_llm_client with mock...")
    from src.core.llm_client import create_llm_client
    client = create_llm_client(LLMConfig(), use_mock=True)
    print("   ✓ LLM client created with mock")
    
    print("7. Testing response generation...")
    response2 = client.generate_response("Another test prompt")
    print(f"   ✓ Response generated (length: {len(response2)})")
    
    print("\nAll simple tests passed!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
