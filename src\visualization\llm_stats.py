"""
LLM Response Visualization and Statistics Module.

This module provides visualization and statistical analysis tools for LLM responses,
helping to evaluate model performance, quality trends, and response patterns.
"""

import os
import json
import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from matplotlib.figure import Figure
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMResponseAnalyzer:
    """Analyzer for LLM response statistics and visualization."""
    
    def __init__(self, results_dir: str = "results/llm_responses", 
                 output_dir: str = "results/visualizations"):
        """
        Initialize the LLM response analyzer.
        
        Args:
            results_dir: Directory where LLM response data is stored
            output_dir: Directory where visualization outputs will be saved
        """
        self.results_dir = Path(results_dir)
        self.output_dir = Path(output_dir)
        
        # Create directories if they don't exist
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize data structures
        self.response_data = []
        self.loaded_files = set()
        
        # Set visual style
        plt.style.use('ggplot')
        
    def load_response_data(self, pattern: str = "*.json") -> int:
        """
        Load LLM response data from JSON files.
        
        Args:
            pattern: File pattern to match
            
        Returns:
            Number of response records loaded
        """
        count = 0
        for file_path in self.results_dir.glob(pattern):
            if file_path in self.loaded_files:
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Add metadata from filename
                if isinstance(data, dict):
                    # Single response
                    data['_file_source'] = str(file_path)
                    data['_timestamp'] = datetime.datetime.fromtimestamp(
                        file_path.stat().st_mtime).isoformat()
                    self.response_data.append(data)
                    count += 1
                elif isinstance(data, list):
                    # List of responses
                    for item in data:
                        if isinstance(item, dict):
                            item['_file_source'] = str(file_path)
                            item['_timestamp'] = datetime.datetime.fromtimestamp(
                                file_path.stat().st_mtime).isoformat()
                            self.response_data.append(item)
                            count += 1
                
                self.loaded_files.add(file_path)
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
                
        logger.info(f"Loaded {count} response records from {len(self.loaded_files)} files")
        return count
    
    def save_response_data(self, file_path: Optional[str] = None) -> str:
        """
        Save collected response data to a JSON file.
        
        Args:
            file_path: Path to save the data, or None to use auto-generated name
            
        Returns:
            Path where data was saved
        """
        if file_path is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.output_dir / f"response_data_{timestamp}.json"
        else:
            file_path = Path(file_path)
            
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.response_data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Saved {len(self.response_data)} response records to {file_path}")
        return str(file_path)
    
    def to_dataframe(self) -> pd.DataFrame:
        """
        Convert response data to a pandas DataFrame for analysis.
        
        Returns:
            DataFrame containing response data
        """
        if not self.response_data:
            logger.warning("No response data available")
            return pd.DataFrame()
            
        return pd.DataFrame(self.response_data)
    
    def _save_figure(self, fig: Figure, filename: str) -> str:
        """
        Save a matplotlib figure to the output directory.
        
        Args:
            fig: Matplotlib figure object
            filename: Filename (without path)
            
        Returns:
            Path where figure was saved
        """
        output_path = self.output_dir / filename
        fig.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        return str(output_path)
        
    def plot_quality_distribution(self, save: bool = True) -> Optional[str]:
        """
        Plot the distribution of quality scores.
        
        Args:
            save: Whether to save the figure to a file
            
        Returns:
            Path where figure was saved, or None if not saved
        """
        df = self.to_dataframe()
        if df.empty or 'quality_score' not in df.columns:
            logger.warning("No quality score data available")
            return None
            
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create histogram with custom bins for quality thresholds
        bins = [0, 0.3, 0.5, 0.7, 1.0]
        labels = ['Critical (<0.3)', 'Low (0.3-0.5)', 'Moderate (0.5-0.7)', 'Good (>0.7)']
        
        counts, edges = np.histogram(df['quality_score'], bins=bins)
        
        # Bar colors based on quality levels
        colors = ['#d62728', '#ff7f0e', '#2ca02c', '#1f77b4']
        
        ax.bar(range(len(counts)), counts, tick_label=labels, color=colors)
        ax.set_title('Distribution of LLM Response Quality Scores', fontsize=14)
        ax.set_ylabel('Number of Responses', fontsize=12)
        ax.set_xlabel('Quality Score Range', fontsize=12)
        
        # Add count labels on top of bars
        for i, count in enumerate(counts):
            ax.text(i, count + 0.5, str(count), ha='center', fontsize=11)
            
        # Add average score as text annotation
        avg_score = df['quality_score'].mean()
        ax.text(0.02, 0.95, f'Average Score: {avg_score:.2f}', 
                transform=ax.transAxes, fontsize=12,
                bbox=dict(facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.datetime.now().strftime("%Y%m%d")
            return self._save_figure(fig, f"quality_distribution_{timestamp}.png")
        
        return None
        
    def plot_quality_by_task(self, save: bool = True) -> Optional[str]:
        """
        Plot average quality scores by task type.
        
        Args:
            save: Whether to save the figure to a file
            
        Returns:
            Path where figure was saved, or None if not saved
        """
        df = self.to_dataframe()
        if df.empty:
            logger.warning("No data available")
            return None
            
        # Extract task types and quality scores
        task_scores = {}
        
        for record in self.response_data:
            if 'task_results' in record and 'quality_score' in record:
                for task_name in record['task_results'].keys():
                    if task_name not in task_scores:
                        task_scores[task_name] = []
                    task_scores[task_name].append(record['quality_score'])
        
        if not task_scores:
            logger.warning("No task-specific quality data available")
            return None
            
        # Calculate average score per task
        task_avg_scores = {task: np.mean(scores) for task, scores in task_scores.items()}
        
        # Sort tasks by average score
        sorted_tasks = sorted(task_avg_scores.items(), key=lambda x: x[1], reverse=True)
        tasks, scores = zip(*sorted_tasks)
        
        # Format task names for display
        task_labels = [t.replace('_', ' ').title() for t in tasks]
        
        # Create bar chart
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Color bars based on quality thresholds
        colors = ['#d62728', '#ff7f0e', '#2ca02c', '#1f77b4']
        bar_colors = [colors[3] if s > 0.7 else 
                     colors[2] if s > 0.5 else 
                     colors[1] if s > 0.3 else 
                     colors[0] for s in scores]
        
        bars = ax.bar(task_labels, scores, color=bar_colors)
        
        # Add value labels on top of bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', fontsize=10)
        
        # Add quality threshold lines
        ax.axhline(y=0.3, color='r', linestyle='--', alpha=0.6)
        ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.6)
        ax.axhline(y=0.7, color='g', linestyle='--', alpha=0.6)
        
        ax.set_title('Average Quality Score by Task Type', fontsize=14)
        ax.set_ylabel('Average Quality Score', fontsize=12)
        ax.set_ylim(0, 1.0)
        plt.xticks(rotation=45, ha='right')
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.datetime.now().strftime("%Y%m%d")
            return self._save_figure(fig, f"quality_by_task_{timestamp}.png")
        
        return None
    
    def plot_response_time_distribution(self, save: bool = True) -> Optional[str]:
        """
        Plot distribution of LLM response times.
        
        Args:
            save: Whether to save the figure to a file
            
        Returns:
            Path where figure was saved, or None if not saved
        """
        df = self.to_dataframe()
        if df.empty or 'duration' not in df.columns:
            logger.warning("No duration data available")
            return None
            
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create histogram
        ax.hist(df['duration'], bins=20, color='#1f77b4', alpha=0.7, edgecolor='black')
        
        # Add statistical annotations
        mean_time = df['duration'].mean()
        median_time = df['duration'].median()
        p95_time = df['duration'].quantile(0.95)
        
        ax.axvline(mean_time, color='r', linestyle='--', label=f'Mean: {mean_time:.2f}s')
        ax.axvline(median_time, color='g', linestyle='--', label=f'Median: {median_time:.2f}s')
        ax.axvline(p95_time, color='orange', linestyle='--', label=f'95th %ile: {p95_time:.2f}s')
        
        ax.set_title('Distribution of LLM Response Times', fontsize=14)
        ax.set_xlabel('Response Time (seconds)', fontsize=12)
        ax.set_ylabel('Number of Responses', fontsize=12)
        ax.legend()
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.datetime.now().strftime("%Y%m%d")
            return self._save_figure(fig, f"response_time_{timestamp}.png")
        
        return None
    
    def plot_language_comparison(self, save: bool = True) -> Optional[str]:
        """
        Compare quality scores between languages (English vs Chinese).
        
        Args:
            save: Whether to save the figure to a file
            
        Returns:
            Path where figure was saved, or None if not saved
        """
        df = self.to_dataframe()
        if df.empty or 'language' not in df.columns or 'quality_score' not in df.columns:
            logger.warning("No language and quality data available")
            return None
            
        # Group by language
        lang_groups = df.groupby('language')
        
        if len(lang_groups) < 2:
            logger.warning("Need at least two languages for comparison")
            return None
            
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create box plots for each language
        lang_groups['quality_score'].boxplot(ax=ax)
        
        # Add average values as text
        for i, (lang, group) in enumerate(lang_groups):
            mean_val = group['quality_score'].mean()
            ax.text(i+1, mean_val + 0.02, f'Avg: {mean_val:.2f}', 
                   ha='center', fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.7))
        
        ax.set_title('Quality Score Comparison by Language', fontsize=14)
        ax.set_ylabel('Quality Score', fontsize=12)
        ax.set_ylim(0, 1.0)
        
        # Add quality threshold lines
        ax.axhline(y=0.3, color='r', linestyle='--', alpha=0.6, label='Critical (0.3)')
        ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.6, label='Low (0.5)')
        ax.axhline(y=0.7, color='g', linestyle='--', alpha=0.6, label='Moderate (0.7)')
        ax.legend()
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.datetime.now().strftime("%Y%m%d")
            return self._save_figure(fig, f"language_comparison_{timestamp}.png")
        
        return None
    
    def plot_quality_trend_over_time(self, save: bool = True) -> Optional[str]:
        """
        Plot quality score trends over time.
        
        Args:
            save: Whether to save the figure to a file
            
        Returns:
            Path where figure was saved, or None if not saved
        """
        df = self.to_dataframe()
        if df.empty or 'quality_score' not in df.columns or '_timestamp' not in df.columns:
            logger.warning("No quality score or timestamp data available")
            return None
            
        # Convert timestamp strings to datetime objects
        df['_timestamp'] = pd.to_datetime(df['_timestamp'])
        
        # Sort by timestamp
        df = df.sort_values('_timestamp')
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot scatter with trend line
        ax.scatter(df['_timestamp'], df['quality_score'], alpha=0.6, color='#1f77b4')
        
        # Add rolling average trendline
        window = min(10, len(df) // 2) if len(df) > 5 else 2
        df['rolling_avg'] = df['quality_score'].rolling(window=window).mean()
        ax.plot(df['_timestamp'], df['rolling_avg'], color='r', linewidth=2, label=f'{window}-point Rolling Avg')
        
        # Add quality threshold lines
        ax.axhline(y=0.3, color='r', linestyle='--', alpha=0.4, label='Critical (0.3)')
        ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.4, label='Low (0.5)')
        ax.axhline(y=0.7, color='g', linestyle='--', alpha=0.4, label='Moderate (0.7)')
        
        ax.set_title('Quality Score Trend Over Time', fontsize=14)
        ax.set_ylabel('Quality Score', fontsize=12)
        ax.set_xlabel('Date/Time', fontsize=12)
        ax.set_ylim(0, 1.0)
        ax.legend()
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if save:
            timestamp = datetime.datetime.now().strftime("%Y%m%d")
            return self._save_figure(fig, f"quality_trend_{timestamp}.png")
        
        return None
        
    def generate_all_visualizations(self) -> Dict[str, str]:
        """
        Generate all available visualizations.
        
        Returns:
            Dictionary of visualization types and their file paths
        """
        visualizations = {}
        
        # Quality distribution
        path = self.plot_quality_distribution()
        if path:
            visualizations['quality_distribution'] = path
            
        # Quality by task
        path = self.plot_quality_by_task()
        if path:
            visualizations['quality_by_task'] = path
            
        # Response time distribution
        path = self.plot_response_time_distribution()
        if path:
            visualizations['response_time'] = path
            
        # Language comparison
        path = self.plot_language_comparison()
        if path:
            visualizations['language_comparison'] = path
            
        # Quality trend over time
        path = self.plot_quality_trend_over_time()
        if path:
            visualizations['quality_trend'] = path
            
        return visualizations
        
    def generate_summary_statistics(self) -> Dict[str, Any]:
        """
        Generate summary statistics for LLM responses.
        
        Returns:
            Dictionary of summary statistics
        """
        df = self.to_dataframe()
        if df.empty:
            logger.warning("No data available for statistics")
            return {}
            
        stats = {
            "total_responses": len(df),
            "date_range": {
                "first": df['_timestamp'].min() if '_timestamp' in df.columns else None,
                "last": df['_timestamp'].max() if '_timestamp' in df.columns else None
            }
        }
        
        # Quality stats
        if 'quality_score' in df.columns:
            stats["quality"] = {
                "mean": df['quality_score'].mean(),
                "median": df['quality_score'].median(),
                "min": df['quality_score'].min(),
                "max": df['quality_score'].max(),
                "std_dev": df['quality_score'].std(),
                "distribution": {
                    "critical": (df['quality_score'] < 0.3).sum(),
                    "low": ((df['quality_score'] >= 0.3) & (df['quality_score'] < 0.5)).sum(),
                    "moderate": ((df['quality_score'] >= 0.5) & (df['quality_score'] < 0.7)).sum(),
                    "good": (df['quality_score'] >= 0.7).sum()
                }
            }
            
        # Duration stats
        if 'duration' in df.columns:
            stats["duration"] = {
                "mean": df['duration'].mean(),
                "median": df['duration'].median(),
                "min": df['duration'].min(),
                "max": df['duration'].max(),
                "p95": df['duration'].quantile(0.95)
            }
            
        # Language stats
        if 'language' in df.columns:
            language_counts = df['language'].value_counts().to_dict()
            stats["languages"] = language_counts
            
            # Quality by language
            if 'quality_score' in df.columns:
                lang_quality = {}
                for lang, group in df.groupby('language'):
                    lang_quality[lang] = {
                        "mean": group['quality_score'].mean(),
                        "median": group['quality_score'].median(),
                        "count": len(group)
                    }
                stats["language_quality"] = lang_quality
                
        # Mode stats
        if 'mode' in df.columns:
            mode_counts = df['mode'].value_counts().to_dict()
            stats["modes"] = mode_counts
            
            # Quality by mode
            if 'quality_score' in df.columns:
                mode_quality = {}
                for mode, group in df.groupby('mode'):
                    mode_quality[mode] = {
                        "mean": group['quality_score'].mean(),
                        "median": group['quality_score'].median(),
                        "count": len(group)
                    }
                stats["mode_quality"] = mode_quality
                
        return stats
