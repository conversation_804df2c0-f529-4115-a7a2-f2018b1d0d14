# Task 2: Portrayal Detection (<PERSON>/<PERSON>ti<PERSON>/Devil)

**System Instruction:** You are a narrative analysis model. Read the input document and the provided definitions. Your task is to identify all instances of Hero, Victim, or Devil portrayals. Return ONLY a valid JSON object matching the Output Schema. Do not include explanations.

**Referenced Definitions:** Please adhere to all rules in `prompt_core_definitions_en.md`.

## Task-Specific Goal

-   Detect actors being framed as a `Hero`, `Victim`, or `Devil`.
-   Provide direct evidence and a brief explanation for each portrayal.
-   Distinguish between narrative framing and factual description.
-   Suggest AI decisions for actor list maintenance.

## Detailed Instructions

### Identifying Hero Portrayals
**Key Indicators:**
- Self-promotional language ("we lead", "we protect", "we ensure")
- Positioning as problem-solver or savior
- Emphasizing protective or beneficial role
- Taking credit for positive outcomes

**Examples:**
- "Google's AI principles ensure responsible development"
- "The FTC stands as the guardian of consumer rights"
- "Our company's innovation will democratize AI for everyone"

### Identifying Victim Portrayals
**Key Requirements:**
- Must show harm from ACTIONS or NARRATIVES (not just policy limitations)
- Actor portrayed as unfairly targeted or bearing undue burden
- Language of suffering, harm, or disadvantage

**Examples:**
- "Small businesses are being crushed by discriminatory regulations"
- "Innovators face unfair attacks from entrenched interests"
- "We have been singled out despite our good faith efforts"

### Identifying Devil Portrayals  
**Key Indicators:**
- Attribution of malicious intent without clear evidence
- Exaggeration of negative impacts or motives
- Loaded language ("reckless", "dangerous", "predatory")
- Moral condemnation beyond factual criticism

**Examples:**
- "Big Tech deliberately exploits user data for profit"
- "These companies show reckless disregard for safety"
- "Their predatory practices harm vulnerable populations"

## Output Schema

```json
{
  "doc_id": "string",
  "portrayals": {
    "skipped": false,
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "quote",
        "explanation": "1-2 sentences grounding why this is Hero/Victim/Devil"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}
```

## Input Parameters

- `doc_id`: string
- `title`: string or null
- `text`: string (the document body)
- `optional_known_actors`: array of strings
