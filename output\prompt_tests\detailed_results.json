{"metadata": {"timestamp": "2025-08-14T13:42:56.411830", "total_tests": 11, "config": {"llm": {"provider": "openai", "model": "gpt-4", "api_key": null, "temperature": 0.0, "max_tokens": 4000, "timeout": 60, "max_retries": 3}, "analysis": {"stakeholder_categories": ["Think Tanks", "Government", "Media", "Corporate", "NGOs and Professional Organizations", "Universities and Research Institutes", "Political Entities", "Consultants and Analysts", "Legal and Industry-specific Bodies", "State Guidelines and Documents", "Others"], "enable_actor_extraction": true, "enable_relationship_analysis": true, "enable_portrayal_analysis": true, "enable_issue_scope_analysis": true, "enable_causal_mechanism_analysis": true, "enable_quality_assessment": true, "min_confidence_score": 0.7, "min_evidence_quality": 0.6, "max_document_length": 10000, "chunk_size": 2000, "overlap_size": 200}, "output": {"include_raw_text": false, "include_confidence_scores": true, "include_quality_metrics": true, "format_schema_validation": true, "pretty_print": true, "output_json": true, "output_csv": false, "output_html": false}, "data_dir": "data", "output_dir": "output", "prompts_dir": "prompts", "logs_dir": "logs", "debug_mode": false, "use_mock_llm": true, "save_intermediate_results": false}}, "results": [{"test_id": "unified_analysis_sample_001_1755150175", "template_name": "unified_analysis", "document_id": "sample_001", "prompt_text": "You are an expert information extraction and narrative analysis model. Analyze the following document and return ONLY valid JSON matching the specified schema.\n\n## TASK OBJECTIVES\n1. Extract actors (real persons/organizations only) and their relationships\n2. Identify narrative portrayals (hero, victim, devil) with evidence\n3. Detect issue scope strategies (expansion/containment) with evidence  \n4. Identify causal mechanisms (intentional/inadvertent) with evidence\n5. Provide AI decisions for actor management if needed\n\n## CRITICAL DEFINITIONS\n- **Actors**: Real persons or organizations only. No abstract/virtual roles\n- **Relationship**: Directed interaction between two actors (e.g., \"regulates\", \"lobbies\", \"collaborates\")\n- **Portrayals**:\n  • Hero: Actor framed as able to fix problems, self-promotional, emphasizing protective role\n  • Victim: Actor framed as harmed by narratives/actions, not merely policy limitations\n  • Devil: Opponents framed as malicious or more evil than reality, exaggerating motives\n- **Issue expansion**: Deliberate broadening of scope, diffusing costs across many while benefits concentrate to few\n- **Issue containment**: Deliberate narrowing of scope to specialists, limiting salience\n- **Causal mechanisms**:\n  • Intentional: Assigns blame deliberately to harm reputations or shift responsibility\n  • Inadvertent: Attributes problems to unintended consequences of policies\n\n## EXTRACTION RULES\n- Extract specific, named actors (e.g., \"FTC\", \"OpenAI\"); avoid categories like \"policymakers\"\n- Maintain directional consistency for relationships (e.g., \"A regulates B\")\n- Split multi-actor mentions into multiple pairwise relationships\n- Use concise verb phrases for relationships\n- Evidence must be direct quotations or tight paraphrases from text\n- If no valid findings, set skipped=true with reason\n\n## EXCLUSION RULES\n- Non-real/virtual roles; hypothetical statements (\"should\", \"could\")\n- Flat factual descriptions without narrative framing\n- Generic concerns without named actors; indirect framings\n- Pure insights/opinions; drawbacks without framing\n- Statements without blame for causal mechanisms\n\n## STAKEHOLDER CATEGORIES\nThink Tanks, Government, Media, Corporate, NGOs and Professional Organizations, Universities and Research Institutes, Political Entities, Consultants and Analysts, Legal and Industry-specific Bodies, State Guidelines and Documents, Others\n\n## OUTPUT SCHEMA\n{\n  \"doc_id\": \"string\",\n  \"title\": \"string|null\",\n  \"actors\": [\n    {\n      \"name\": \"string\",\n      \"stakeholder_category\": \"category from list above\",\n      \"notes\": \"string|null\"\n    }\n  ],\n  \"relationships\": {\n    \"skipped\": \"boolean\",\n    \"skip_reason\": \"string|null\", \n    \"items\": [\n      {\n        \"a_name\": \"string\",\n        \"b_name\": \"string\",\n        \"relationship\": \"string\",\n        \"a_character\": \"hero|villain|victim|null\",\n        \"b_character\": \"hero|villain|victim|null\", \n        \"a_type\": \"stakeholder category\",\n        \"b_type\": \"stakeholder category\",\n        \"evidence\": \"quote or text span\"\n      }\n    ]\n  },\n  \"portrayals\": {\n    \"skipped\": \"boolean\",\n    \"skip_reason\": \"string|null\",\n    \"items\": [\n      {\n        \"actor\": \"string\",\n        \"type\": \"Hero|Victim|Devil\",\n        \"evidence\": \"quote\",\n        \"explanation\": \"1-2 sentences grounding the portrayal\"\n      }\n    ],\n    \"shifts\": {\n      \"angel_shift_present\": \"boolean\",\n      \"devil_shift_present\": \"boolean\", \n      \"angel_shift_evidence\": \"string|null\",\n      \"devil_shift_evidence\": \"string|null\"\n    }\n  },\n  \"issue_scope\": {\n    \"skipped\": \"boolean\",\n    \"skip_reason\": \"string|null\",\n    \"items\": [\n      {\n        \"actor\": \"string\", \n        \"type\": \"Issue expansion|Issue containment\",\n        \"evidence\": \"quote\",\n        \"explanation\": \"1-2 sentences explaining the strategy\"\n      }\n    ]\n  },\n  \"causal_mechanisms\": {\n    \"skipped\": \"boolean\",\n    \"skip_reason\": \"string|null\", \n    \"items\": [\n      {\n        \"actor\": \"string\",\n        \"type\": \"Intentional|Inadvertent\",\n        \"evidence\": \"quote or passage\",\n        \"explanation\": \"1-2 sentences on blame assignment\"\n      }\n    ]\n  },\n  \"ai_decisions\": [\n    {\n      \"action\": \"add_actor|remove_actor|none\",\n      \"actor\": \"string|null\",\n      \"reasoning\": \"string\"\n    }\n  ]\n}\n\n## INPUT DATA\ndoc_id: sample_001\ntitle: FTC Tech Regulations\ntext: |\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson Jane Smith defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO Satya Nadella called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\nknown_actors: \n\nReturn ONLY valid JSON. No explanations outside the schema.", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.1004643440246582, "token_count": 640, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.405697"}, {"test_id": "actor_extraction_sample_001_1755150175", "template_name": "actor_extraction", "document_id": "sample_001", "prompt_text": "You are an expert entity extraction specialist. Your task is to identify ALL real persons and organizations mentioned in the document.\n\nCRITICAL REQUIREMENTS:\n- Extract ONLY real persons and organizations (no abstract concepts, roles, or categories)\n- Include every named entity regardless of how minor they seem\n- For each actor, determine their stakeholder category from the provided list\n- Provide brief notes about who they are if available\n- Do NOT include: generic roles, abstract concepts, or unnamed groups\n\nSTAKEHOLDER CATEGORIES:\n[\"Think Tanks\",\"Government\",\"Media\",\"Corporate\",\"NGOs and Professional Organizations\",\"Universities and Research Institutes\",\"Political Entities\",\"Consultants and Analysts\",\"Legal and Industry-specific Bodies\",\"State Guidelines and Documents\",\"Others\"]\n\nEXAMPLES:\n✅ GOOD: \"FTC\", \"Google\", \"Microsoft\", \"Consumer advocacy groups\", \"<PERSON>\"\n❌ BAD: \"policymakers\", \"regulators\", \"the public\", \"companies\"\n\nReturn ONLY valid JSON with this structure:\n{\n  \"actors\": [\n    {\n      \"name\": \"string\",\n      \"stakeholder_category\": \"category from list\",\n      \"notes\": \"string|null\"\n    }\n  ]\n}", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10046505928039551, "token_count": 194, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.506162"}, {"test_id": "relationship_analysis_sample_001_1755150175", "template_name": "relationship_analysis", "document_id": "sample_001", "prompt_text": "You are an expert relationship analyst. Your task is to analyze relationships between the provided actors.\n\nANALYSIS REQUIREMENTS:\n- Analyze ALL pairwise relationships between actors\n- Identify directed relationships (A regulates B, B lobbies A)\n- Use specific verb phrases for relationships\n- Character assignments: hero, villain, victim (or null)\n- Evidence must be direct quotes or tight paraphrases\n- Consider relationship directionality and power dynamics\n\nRELATIONSHIP TYPES:\n- Regulatory: regulates, oversees, monitors, enforces\n- Opposition: criticizes, opposes, challenges, questions\n- Support: supports, endorses, advocates for, defends\n- Collaboration: works with, partners with, collaborates with\n- Influence: lobbies, pressures, persuades, influences\n- Conflict: conflicts with, competes with, disputes with\n\nCHARACTER DEFINITIONS:\n- HERO: Takes positive action, solves problems, protects others\n- VILLAIN: Acts maliciously, opposes progress, harms others  \n- VICTIM: Suffers harm, bears consequences, is affected negatively\n\nReturn ONLY valid JSON with this structure:\n{\n  \"relationships\": {\n    \"skipped\": false,\n    \"skip_reason\": null,\n    \"items\": [\n      {\n        \"a_name\": \"string\",\n        \"b_name\": \"string\", \n        \"relationship\": \"string\",\n        \"a_character\": \"hero|villain|victim|null\",\n        \"b_character\": \"hero|villain|victim|null\",\n        \"a_type\": \"stakeholder category\",\n        \"b_type\": \"stakeholder category\", \n        \"evidence\": \"quote from text\"\n      }\n    ]\n  }\n}", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.1004338264465332, "token_count": 233, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.606596"}, {"test_id": "portrayal_analysis_sample_001_1755150175", "template_name": "portrayal_analysis", "document_id": "sample_001", "prompt_text": "You are an expert narrative analysis specialist. Your task is to detect hero/victim/devil portrayals.\n\nPORTRAYAL DEFINITIONS:\n- HERO: Actor framed as able to fix problems, self-promotional, protective role\n- VICTIM: Actor framed as harmed or bearing consequences from narratives/actions  \n- DEVIL: Opponents framed as malicious or more evil than they are\n\nANALYSIS REQUIREMENTS:\n- Look for specific framing language and narrative positioning\n- Evidence must be direct quotes showing the portrayal\n- Provide 1-2 sentence explanations grounding the portrayal\n- Detect angel/devil shifts if present\n\nFRAMING INDICATORS:\nHERO LANGUAGE:\n- \"took action\", \"stepped in\", \"protected\", \"saved\", \"championed\"\n- \"leading the way\", \"pioneering\", \"innovative solution\"\n- \"advocate for\", \"defender of\", \"champion for\"\n\nVICTIM LANGUAGE:\n- \"harmed by\", \"suffered\", \"affected by\", \"bearing the cost\"\n- \"vulnerable to\", \"at risk from\", \"threatened by\"\n- \"disproportionately affected\", \"bearing the burden\"\n\nDEVIL LANGUAGE:\n- \"malicious\", \"evil\", \"corrupt\", \"selfish\", \"greedy\"\n- \"exploiting\", \"manipulating\", \"deceiving\", \"endangering\"\n- \"irresponsible\", \"reckless\", \"harmful\", \"dangerous\"\n\nNARRATIVE SHIFTS:\n- ANGEL SHIFT: Framing oneself or allies as heroes\n- DEVIL SHIFT: Framing opponents as devils or villains\n\nReturn ONLY valid JSON with this structure:\n{\n  \"portrayals\": {\n    \"skipped\": false,\n    \"skip_reason\": null,\n    \"items\": [\n      {\n        \"actor\": \"string\",\n        \"type\": \"Hero|Victim|Devil\",\n        \"evidence\": \"quote\",\n        \"explanation\": \"1-2 sentences grounding why this is Hero/Victim/Devil\"\n      }\n    ],\n    \"shifts\": {\n      \"angel_shift_present\": \"boolean\",\n      \"devil_shift_present\": \"boolean\", \n      \"angel_shift_evidence\": \"string|null\",\n      \"devil_shift_evidence\": \"string|null\"\n    }\n  }\n}", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10059666633605957, "token_count": 275, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.707193"}, {"test_id": "issue_scope_analysis_sample_001_1755150175", "template_name": "issue_scope_analysis", "document_id": "sample_001", "prompt_text": "You are an expert issue scope analyst. Your task is to detect issue expansion and containment strategies.\n\nSCOPE STRATEGY DEFINITIONS:\n- ISSUE EXPANSION: Deliberate broadening of scope/audience, diffusing costs across many while benefits concentrate to few\n- ISSUE CONTAINMENT: Deliberate narrowing of scope/audience to specialists, limiting salience\n\nANALYSIS REQUIREMENTS:\n- Identify deliberate attempts to broaden or narrow issue scope\n- Look for language that expands/contracts the audience or implications\n- Evidence must show the strategic broadening/narrowing\n- Provide explanation of the scope strategy\n\nISSUE EXPANSION INDICATORS:\n- \"affects everyone\", \"national crisis\", \"global impact\"\n- \"entire industry\", \"all consumers\", \"widespread consequences\"\n- \"systemic problem\", \"broader implications\", \"far-reaching\"\n- Moving from specific to general, from local to universal\n\nISSUE CONTAINMENT INDICATORS:\n- \"technical matter\", \"specialized issue\", \"expert domain\"\n- \"limited impact\", \"isolated incident\", \"specific case\"\n- \"internal matter\", \"operational details\", \"procedural\"\n- Moving from general to specific, from universal to local\n\nSTRATEGIC QUESTIONS:\n- Who benefits from broadening/narrowing the scope?\n- Who bears costs in each scenario?\n- What language is used to include/exclude stakeholders?\n- How does the scope change affect the perceived importance?\n\nReturn ONLY valid JSON with this structure:\n{\n  \"issue_scope\": {\n    \"skipped\": false,\n    \"skip_reason\": null,\n    \"items\": [\n      {\n        \"actor\": \"string\",\n        \"type\": \"Issue expansion|Issue containment\",\n        \"evidence\": \"quote\",\n        \"explanation\": \"1-2 sentences explaining the deliberate broadening/narrowing\"\n      }\n    ]\n  }\n}", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10054731369018555, "token_count": 274, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.807740"}, {"test_id": "causal_mechanism_analysis_sample_001_1755150175", "template_name": "causal_mechanism_analysis", "document_id": "sample_001", "prompt_text": "You are an expert causal mechanism analyst. Your task is to detect causal attributions.\n\nCAUSAL MECHANISM DEFINITIONS:\n- INTENTIONAL: Assigns blame deliberately to harm others' reputations or shift responsibility\n- INADVERTENT: Attributes problems to unintended consequences of a policy\n\nANALYSIS REQUIREMENTS:\n- Identify blame assignment and causal reasoning\n- Distinguish between intentional blame and unintended consequences\n- Evidence must show the causal reasoning pattern\n- Provide explanation of the causal mechanism\n\nINTENTIONAL BLAME INDICATORS:\n- \"deliberately\", \"intentionally\", \"knowingly\", \"purposely\"\n- \"responsible for\", \"to blame for\", \"caused the problem\"\n- \"malicious intent\", \"deliberate action\", \"conscious decision\"\n- Language suggesting moral responsibility and agency\n\nINADVERTENT CONSEQUENCE INDICATORS:\n- \"unintended\", \"unforeseen\", \"unexpected\", \"inadvertent\"\n- \"side effect\", \"unintentional consequence\", \"unforeseen outcome\"\n- \"despite good intentions\", \"well-meaning but...\"\n- Language suggesting systemic or structural causes\n\nCAUSAL ANALYSIS QUESTIONS:\n- Is the cause attributed to human agency or systemic factors?\n- Is there language suggesting intent or lack thereof?\n- How is responsibility distributed among actors?\n- What type of causal reasoning is being employed?\n\nReturn ONLY valid JSON with this structure:\n{\n  \"causal_mechanisms\": {\n    \"skipped\": false,\n    \"skip_reason\": null,\n    \"items\": [\n      {\n        \"actor\": \"string\",\n        \"type\": \"Intentional|Inadvertent\", \n        \"evidence\": \"quote or short passage\",\n        \"explanation\": \"1-2 sentences on blame assignment or unintended consequences\"\n      }\n    ]\n  }\n}", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10093188285827637, "token_count": 263, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:55.908672"}, {"test_id": "quality_assessment_sample_001_1755150175", "template_name": "quality_assessment", "document_id": "sample_001", "prompt_text": "Assess the quality and completeness of this document analysis. Return ONLY valid JSON.\n\n## QUALITY METRICS\n- **Entity Coverage**: Are all significant actors identified and categorized?\n- **Relationship Completeness**: Are key relationships captured with evidence?\n- **Narrative Depth**: Are portrayals and strategies well-identified?\n- **Evidence Quality**: Is evidence sufficient and well-grounded?\n- **Consistency**: Are findings consistent across the document?\n\n## OUTPUT SCHEMA\n{\n  \"doc_id\": \"sample_001\",\n  \"quality_assessment\": {\n    \"overall_score\": 0.0 to 1.0,\n    \"entity_coverage\": 0.0 to 1.0,\n    \"relationship_completeness\": 0.0 to 1.0, \n    \"narrative_depth\": 0.0 to 1.0,\n    \"evidence_quality\": 0.0 to 1.0,\n    \"consistency\": 0.0 to 1.0,\n    \"strengths\": [\"string\"],\n    \"weaknesses\": [\"string\"],\n    \"recommendations\": [\"string\"]\n  }\n}\n\n## DOCUMENT\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson <PERSON> defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO <PERSON><PERSON><PERSON> called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\n\n{{#ANALYSIS_RESULTS}}\n## ANALYSIS RESULTS\n{{{ANALYSIS_RESULTS}}}\n{{/ANALYSIS_RESULTS}}\n\nReturn ONLY valid JSON.", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.1005101203918457, "token_count": 266, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:56.009182"}, {"test_id": "validation_check_sample_001_1755150176", "template_name": "validation_check", "document_id": "sample_001", "prompt_text": "Validate this analysis against the original document for accuracy and completeness. Return ONLY valid JSON.\n\n## VALIDATION CRITERIA\n- **Actor Accuracy**: Are all actors correctly identified and categorized?\n- **Relationship Accuracy**: Are relationships correctly captured and directed?\n- **Evidence Validity**: Does evidence accurately reflect the source text?\n- **Completeness**: Are any significant elements missing?\n- **Schema Compliance**: Does output match required schema?\n\n## OUTPUT SCHEMA\n{\n  \"doc_id\": \"sample_001\",\n  \"validation\": {\n    \"passed\": \"boolean\",\n    \"errors\": [\n      {\n        \"type\": \"actor|relationship|evidence|completeness|schema\",\n        \"message\": \"string\",\n        \"severity\": \"low|medium|high\"\n      }\n    ],\n    \"warnings\": [\"string\"],\n    \"corrections_needed\": \"boolean\",\n    \"suggestions\": [\"string\"]\n  }\n}\n\n## DOCUMENT\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson <PERSON> defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO <PERSON><PERSON><PERSON> called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\n\n{{#ANALYSIS_RESULTS}}\n## ANALYSIS RESULTS\n{{{ANALYSIS_RESULTS}}}\n{{/ANALYSIS_RESULTS}}\n\nReturn ONLY valid JSON.", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10064697265625, "token_count": 258, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:56.109829"}, {"test_id": "openai_optimized_sample_001_1755150176", "template_name": "openai_optimized", "document_id": "sample_001", "prompt_text": "You are an expert information extraction and narrative analysis model. Analyze this document and return ONLY valid JSON.\n\nFocus on extracting actors, relationships, portrayals, issue scope strategies, and causal mechanisms with precise evidence.\n\n## OUTPUT SCHEMA\n{\n  \"doc_id\": \"sample_001\",\n  \"title\": \"FTC Tech Regulations\", \n  \"actors\": [{\"name\": \"string\", \"stakeholder_category\": \"string\", \"notes\": \"string|null\"}],\n  \"relationships\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"a_name\": \"string\", \"b_name\": \"string\", \"relationship\": \"string\", \"a_character\": \"hero|villain|victim|null\", \"b_character\": \"hero|villain|victim|null\", \"a_type\": \"string\", \"b_type\": \"string\", \"evidence\": \"string\"}]},\n  \"portrayals\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Hero|Victim|Devil\", \"evidence\": \"string\", \"explanation\": \"string\"}], \"shifts\": {\"angel_shift_present\": \"boolean\", \"devil_shift_present\": \"boolean\", \"angel_shift_evidence\": \"string|null\", \"devil_shift_evidence\": \"string|null\"}},\n  \"issue_scope\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Issue expansion|Issue containment\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"causal_mechanisms\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Intentional|Inadvertent\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"ai_decisions\": [{\"action\": \"add_actor|remove_actor|none\", \"actor\": \"string|null\", \"reasoning\": \"string\"}]\n}\n\n## DOCUMENT\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson Jane Smith defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO Satya Nadella called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\n\nReturn ONLY valid JSON.", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10078692436218262, "token_count": 294, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:56.210616"}, {"test_id": "anthropic_optimized_sample_001_1755150176", "template_name": "anthropic_optimized", "document_id": "sample_001", "prompt_text": "I need you to analyze a document and extract specific information about actors, relationships, and narrative patterns.\n\nPlease analyze this document and return a structured JSON response with the following components:\n\n1. **Actors**: Extract all real persons and organizations, categorizing them as: Think Tanks, Government, Media, Corporate, NGOs and Professional Organizations, Universities and Research Institutes, Political Entities, Consultants and Analysts, Legal and Industry-specific Bodies, State Guidelines and Documents, or Others.\n\n2. **Relationships**: Identify directed relationships between actors, noting how they interact (regulates, lobbies, collaborates, etc.) and any character portrayals (hero, villain, victim).\n\n3. **Portrayals**: Look for hero/victim/devil framing - where actors position themselves as problem-solvers, victims of harm, or opponents as malicious.\n\n4. **Issue Scope**: Identify attempts to broaden (issue expansion) or narrow (issue containment) the scope of the issue.\n\n5. **Causal Mechanisms**: Find intentional blame assignment or attribution of problems to unintended consequences.\n\n## OUTPUT SCHEMA\n{\n  \"doc_id\": \"sample_001\",\n  \"title\": \"FTC Tech Regulations\",\n  \"actors\": [{\"name\": \"string\", \"stakeholder_category\": \"string\", \"notes\": \"string|null\"}],\n  \"relationships\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"a_name\": \"string\", \"b_name\": \"string\", \"relationship\": \"string\", \"a_character\": \"hero|villain|victim|null\", \"b_character\": \"hero|villain|victim|null\", \"a_type\": \"string\", \"b_type\": \"string\", \"evidence\": \"string\"}]},\n  \"portrayals\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Hero|Victim|Devil\", \"evidence\": \"string\", \"explanation\": \"string\"}], \"shifts\": {\"angel_shift_present\": \"boolean\", \"devil_shift_present\": \"boolean\", \"angel_shift_evidence\": \"string|null\", \"devil_shift_evidence\": \"string|null\"}},\n  \"issue_scope\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Issue expansion|Issue containment\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"causal_mechanisms\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Intentional|Inadvertent\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"ai_decisions\": [{\"action\": \"add_actor|remove_actor|none\", \"actor\": \"string|null\", \"reasoning\": \"string\"}]\n}\n\n## DOCUMENT\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson Jane Smith defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO Satya Nadella called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\n\nReturn your response as valid JSON only.", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10079383850097656, "token_count": 408, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:56.311410"}, {"test_id": "zhipu_optimized_sample_001_1755150176", "template_name": "zhipu_optimized", "document_id": "sample_001", "prompt_text": "请分析以下文档，提取关键信息并返回JSON格式的结果。\n\n## 分析任务\n1. **提取行动者**: 识别文档中的真实人物和组织机构\n2. **分析关系**: 确定行动者之间的 directed 关系（如监管、游说、合作等）\n3. **识别角色定位**: 发现英雄、受害者、恶魔等角色塑造\n4. **问题范围策略**: 识别问题扩大化或缩小化策略\n5. **因果机制**: 分析有意或无意的归因机制\n\n## 关键定义\n- **行动者**: 真实的人物或组织，不包括抽象角色\n- **关系**: 两个行动者之间的 directed 互动\n- **角色塑造**: \n  • 英雄: 塑造为解决问题者\n  • 受害者: 塑造为受到伤害者  \n  • 恶魔: 对手被塑造为恶意者\n- **问题扩大化**: 故意扩大问题范围，分散成本，集中收益\n- **问题缩小化**: 限制问题范围，降低显著性\n- **因果机制**:\n  • 有意: 故意归咎于他人\n  • 无意: 归因于政策意外后果\n\n## 利益相关者类别\nThink Tanks, Government, Media, Corporate, NGOs and Professional Organizations, Universities and Research Institutes, Political Entities, Consultants and Analysts, Legal and Industry-specific Bodies, State Guidelines and Documents, Others\n\n## 输出格式 (OUTPUT SCHEMA)\n{\n  \"doc_id\": \"sample_001\",\n  \"title\": \"FTC Tech Regulations\",\n  \"actors\": [{\"name\": \"string\", \"stakeholder_category\": \"string\", \"notes\": \"string|null\"}],\n  \"relationships\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"a_name\": \"string\", \"b_name\": \"string\", \"relationship\": \"string\", \"a_character\": \"hero|villain|victim|null\", \"b_character\": \"hero|villain|victim|null\", \"a_type\": \"string\", \"b_type\": \"string\", \"evidence\": \"string\"}]},\n  \"portrayals\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Hero|Victim|Devil\", \"evidence\": \"string\", \"explanation\": \"string\"}], \"shifts\": {\"angel_shift_present\": \"boolean\", \"devil_shift_present\": \"boolean\", \"angel_shift_evidence\": \"string|null\", \"devil_shift_evidence\": \"string|null\"}},\n  \"issue_scope\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Issue expansion|Issue containment\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"causal_mechanisms\": {\"skipped\": \"boolean\", \"skip_reason\": \"string|null\", \"items\": [{\"actor\": \"string\", \"type\": \"Intentional|Inadvertent\", \"evidence\": \"string\", \"explanation\": \"string\"}]},\n  \"ai_decisions\": [{\"action\": \"add_actor|remove_actor|none\", \"actor\": \"string|null\", \"reasoning\": \"string\"}]\n}\n\n## 文档内容\nThe Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies \n    like Google and Microsoft. According to the FTC, these companies have been engaging in \n    anti-competitive practices that harm consumers and stifle innovation. \n    \n    Google spokesperson Jane Smith defended the company's practices, stating that they \n    \"operate within the law and promote healthy competition.\" However, consumer advocacy \n    groups argue that the regulations don't go far enough to protect users from \n    monopolistic behavior.\n    \n    Microsoft CEO Satya Nadella called for a balanced approach that protects consumers \n    while encouraging technological innovation. The debate continues as policymakers \n    consider the broader implications for the tech industry.\n\n请只返回有效的JSON结果。", "response_text": "{\"doc_id\": \"mock_doc\", \"title\": \"Mock Document\", \"actors\": [{\"name\": \"Mock Actor\", \"stakeholder_category\": \"Corporate\", \"notes\": \"Mock actor for testing\"}], \"relationships\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"portrayals\": {\"skipped\": false, \"skip_reason\": null, \"items\": [], \"shifts\": {\"angel_shift_present\": false, \"devil_shift_present\": false, \"angel_shift_evidence\": null, \"devil_shift_evidence\": null}}, \"issue_scope\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"causal_mechanisms\": {\"skipped\": false, \"skip_reason\": null, \"items\": []}, \"ai_decisions\": []}", "parsed_result": {"doc_id": "mock_doc", "title": "<PERSON><PERSON> Document", "actors": [{"name": "<PERSON>ck Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}], "relationships": {"skipped": false, "skip_reason": null, "items": []}, "portrayals": {"skipped": false, "skip_reason": null, "items": [], "shifts": {"angel_shift_present": false, "devil_shift_present": false, "angel_shift_evidence": null, "devil_shift_evidence": null}}, "issue_scope": {"skipped": false, "skip_reason": null, "items": []}, "causal_mechanisms": {"skipped": false, "skip_reason": null, "items": []}, "ai_decisions": []}, "parse_success": true, "execution_time": 0.10042071342468262, "token_count": 343, "error_message": null, "quality_score": 0.1, "timestamp": "2025-08-14T13:42:56.411830"}], "metrics": {"total_tests": 11, "successful_tests": 11, "failed_tests": 0, "average_execution_time": 0.10059978745200417, "average_quality_score": 0.1, "parse_success_rate": 1.0, "template_performance": {"unified_analysis": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.1004643440246582, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "actor_extraction": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10046505928039551, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "relationship_analysis": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.1004338264465332, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "portrayal_analysis": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10059666633605957, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "issue_scope_analysis": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10054731369018555, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "causal_mechanism_analysis": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10093188285827637, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "quality_assessment": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.1005101203918457, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "validation_check": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10064697265625, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "openai_optimized": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10078692436218262, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "anthropic_optimized": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10079383850097656, "avg_quality_score": 0.1, "parse_success_rate": 1.0}, "zhipu_optimized": {"total_tests": 1, "successful_tests": 1, "avg_execution_time": 0.10042071342468262, "avg_quality_score": 0.1, "parse_success_rate": 1.0}}}}