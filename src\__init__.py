"""
Document Analysis System - Python Implementation

A comprehensive document analysis system that extracts actors, relationships, 
and narrative patterns from text documents using Large Language Models (LLMs).

Features:
- Actor Extraction: Identifies real persons and organizations in documents
- Relationship Analysis: Extracts pairwise relationships between actors
- Narrative Pattern Detection: Identifies hero/victim/devil portrayals and narrative shifts
- Issue Scope Analysis: Detects issue expansion and containment strategies
- Causal Mechanism Detection: Identifies intentional blame and inadvertent consequences
- Quality Control: Validates extracted information against source text
- Batch Processing: Process multiple documents efficiently
- Multiple LLM Support: Works with OpenAI GPT and Anthropic Claude models
- Prompt Testing Framework: Test and evaluate different prompt templates
"""

__version__ = "1.0.0"
__author__ = "Document Analysis Team"
__email__ = "<EMAIL>"

from .core.analyzer import DocumentAnalyzer
from .core.config import Config
from .core.prompts import PromptTemplates
from .utils.document_reader import DocumentReader
from .testing.prompt_tester import PromptTester

__all__ = [
    "DocumentAnalyzer",
    "Config", 
    "PromptTemplates",
    "DocumentReader",
    "PromptTester"
]