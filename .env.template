# OpenAI API Key (for GPT models)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LLM Provider (openai or anthropic)
LLM_PROVIDER=openai

# LLM Model
LLM_MODEL=gpt-4

# Use mock LLM for testing (true/false)
USE_MOCK_LLM=true

# LLM Settings
LLM_TEMPERATURE=0
LLM_MAX_TOKENS=4000
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3

# Analysis Settings
MIN_CONFIDENCE_SCORE=0.7
MAX_DOCUMENT_LENGTH=10000
ENABLE_QUALITY_ASSESSMENT=true

# Output Settings
OUTPUT_DIR=output
INCLUDE_CONFIDENCE_SCORES=true
PRETTY_PRINT=true
DEBUG_MODE=false