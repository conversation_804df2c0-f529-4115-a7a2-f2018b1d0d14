"""
Modular prompt system for policy narrative analysis.

This module implements a flexible, task-specific prompt system based on
policy narrative theory. It supports both unified and stepwise analysis modes.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum

logger = logging.getLogger(__name__)

class AnalysisMode(Enum):
    """Analysis mode enumeration."""
    UNIFIED = "unified"  # Single comprehensive analysis
    STEPWISE = "stepwise"  # Task-by-task analysis
    SELECTIVE = "selective"  # Selected tasks only

class TaskType(Enum):
    """Task type enumeration for modular analysis."""
    ACTORS_RELATIONSHIPS = "actors_relationships"
    PORTRAYALS = "portrayals"
    ISSUE_SCOPE = "issue_scope"
    CAUSAL_MECHANISMS = "causal_mechanisms"

@dataclass
class ModularPrompt:
    """Data class for modular prompt template."""
    task_type: TaskType
    name: str
    description: str
    template_en: str
    template_zh: str
    estimated_tokens: int
    dependencies: List[TaskType] = field(default_factory=list)
    output_schema: Dict[str, Any] = field(default_factory=dict)
    
    def get_template(self, language: str = "en") -> str:
        """Get template in specified language."""
        if language == "zh":
            return self.template_zh
        return self.template_en

@dataclass
class AnalysisContext:
    """Context for analysis execution."""
    doc_id: str
    title: Optional[str]
    text: str
    language: str = "en"
    known_actors: List[str] = field(default_factory=list)
    previous_results: Dict[TaskType, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class ModularPromptSystem:
    """Manages modular prompts for policy narrative analysis."""
    
    def __init__(self, prompts_dir: Optional[Path] = None):
        """Initialize the modular prompt system.
        
        Args:
            prompts_dir: Directory containing prompt template files
        """
        self.prompts_dir = prompts_dir or Path(__file__).parent.parent.parent
        self.core_definitions: Dict[str, str] = {}
        self.task_prompts: Dict[TaskType, ModularPrompt] = {}
        self._load_prompts()
    
    def _load_prompts(self) -> None:
        """Load all prompt templates from files."""
        # Load core definitions
        self._load_core_definitions()
        
        # Load task-specific prompts
        self._load_task_prompts()
        
        logger.info(f"Loaded {len(self.task_prompts)} modular prompts")
    
    def _load_core_definitions(self) -> None:
        """Load core definitions for both languages."""
        for lang in ["en", "zh"]:
            def_file = self.prompts_dir / f"prompt_core_definitions_{lang}.md"
            if def_file.exists():
                with open(def_file, "r", encoding="utf-8") as f:
                    self.core_definitions[lang] = f.read()
                logger.debug(f"Loaded core definitions for {lang}")
    
    def _load_task_prompts(self) -> None:
        """Load all task-specific prompt templates."""
        # Task 1: Actors and Relationships
        self._load_task_prompt(
            TaskType.ACTORS_RELATIONSHIPS,
            "prompt_task1_actors_relationships",
            "Extract actors and identify relationships",
            estimated_tokens=1000,
            output_schema={
                "actors": "array",
                "relationships": "object",
                "ai_decisions": "array"
            }
        )
        
        # Task 2: Portrayals
        self._load_task_prompt(
            TaskType.PORTRAYALS,
            "prompt_task2_portrayals",
            "Detect hero/victim/devil portrayals",
            estimated_tokens=800,
            dependencies=[TaskType.ACTORS_RELATIONSHIPS],
            output_schema={
                "portrayals": "object"
            }
        )
        
        # Task 3: Issue Scope
        self._load_task_prompt(
            TaskType.ISSUE_SCOPE,
            "prompt_task3_issue_scope",
            "Identify issue expansion/containment strategies",
            estimated_tokens=800,
            dependencies=[TaskType.ACTORS_RELATIONSHIPS],
            output_schema={
                "issue_scope": "object"
            }
        )
        
        # Task 4: Causal Mechanisms
        self._load_task_prompt(
            TaskType.CAUSAL_MECHANISMS,
            "prompt_task4_causal_mechanisms",
            "Detect intentional/inadvertent causal mechanisms",
            estimated_tokens=800,
            dependencies=[TaskType.ACTORS_RELATIONSHIPS],
            output_schema={
                "causal_mechanisms": "object",
                "ai_decisions": "array"
            }
        )
    
    def _load_task_prompt(
        self,
        task_type: TaskType,
        file_prefix: str,
        description: str,
        estimated_tokens: int,
        dependencies: Optional[List[TaskType]] = None,
        output_schema: Optional[Dict[str, Any]] = None
    ) -> None:
        """Load a specific task prompt template.
        
        Args:
            task_type: The type of analysis task
            file_prefix: Prefix of the prompt file name
            description: Description of the task
            estimated_tokens: Estimated token usage
            dependencies: Other tasks this depends on
            output_schema: Expected output schema
        """
        template_en = ""
        template_zh = ""
        
        # Load English template
        en_file = self.prompts_dir / f"{file_prefix}_en.md"
        if en_file.exists():
            with open(en_file, "r", encoding="utf-8") as f:
                template_en = f.read()
        
        # Load Chinese template
        zh_file = self.prompts_dir / f"{file_prefix}_zh.md"
        if zh_file.exists():
            with open(zh_file, "r", encoding="utf-8") as f:
                template_zh = f.read()
        
        # Create modular prompt
        self.task_prompts[task_type] = ModularPrompt(
            task_type=task_type,
            name=file_prefix,
            description=description,
            template_en=template_en,
            template_zh=template_zh,
            estimated_tokens=estimated_tokens,
            dependencies=dependencies or [],
            output_schema=output_schema or {}
        )
    
    def get_prompt(
        self,
        task_type: TaskType,
        context: AnalysisContext
    ) -> str:
        """Get formatted prompt for a specific task.
        
        Args:
            task_type: Type of analysis task
            context: Analysis context with document and previous results
            
        Returns:
            Formatted prompt string
        """
        if task_type not in self.task_prompts:
            raise ValueError(f"Unknown task type: {task_type}")
        
        prompt = self.task_prompts[task_type]
        template = prompt.get_template(context.language)
        
        # Format the template with context variables
        formatted = self._format_template(template, context, task_type)
        
        return formatted
    
    def _format_template(
        self,
        template: str,
        context: AnalysisContext,
        task_type: TaskType
    ) -> str:
        """Format a template with context variables.
        
        Args:
            template: Template string
            context: Analysis context
            task_type: Type of task
            
        Returns:
            Formatted template string
        """
        # Prepare variables based on task type
        variables = {
            "doc_id": context.doc_id,
            "title": context.title or "",
            "text": context.text
        }
        
        # Add known actors if available and needed
        if task_type in [TaskType.PORTRAYALS, TaskType.ISSUE_SCOPE, TaskType.CAUSAL_MECHANISMS]:
            if context.known_actors:
                variables["optional_known_actors"] = json.dumps(context.known_actors, ensure_ascii=False)
            else:
                # Extract actors from previous results if available
                if TaskType.ACTORS_RELATIONSHIPS in context.previous_results:
                    actors_result = context.previous_results[TaskType.ACTORS_RELATIONSHIPS]
                    if "actors" in actors_result:
                        actor_names = [a["name"] for a in actors_result["actors"]]
                        variables["optional_known_actors"] = json.dumps(actor_names, ensure_ascii=False)
        
        # Replace placeholders
        for key, value in variables.items():
            template = template.replace(f"{{{{{key}}}}}", str(value))
        
        return template
    
    def get_unified_prompt(
        self,
        context: AnalysisContext,
        tasks: Optional[List[TaskType]] = None
    ) -> str:
        """Get a unified prompt combining multiple tasks.
        
        Args:
            context: Analysis context
            tasks: List of tasks to include (None for all)
            
        Returns:
            Combined prompt string
        """
        if tasks is None:
            tasks = list(TaskType)
        
        # Start with core definitions
        combined = self.core_definitions.get(context.language, "")
        combined += "\n\n"
        
        # Add each task prompt
        for task in tasks:
            if task in self.task_prompts:
                prompt = self.task_prompts[task]
                template = prompt.get_template(context.language)
                combined += f"## {prompt.description}\n\n"
                combined += template
                combined += "\n\n"
        
        # Format with context
        return self._format_template(combined, context, TaskType.ACTORS_RELATIONSHIPS)
    
    def get_task_dependencies(self, task_type: TaskType) -> List[TaskType]:
        """Get dependencies for a specific task.
        
        Args:
            task_type: Task to check dependencies for
            
        Returns:
            List of dependent task types
        """
        if task_type not in self.task_prompts:
            return []
        
        return self.task_prompts[task_type].dependencies
    
    def estimate_tokens(
        self,
        tasks: Optional[List[TaskType]] = None,
        mode: AnalysisMode = AnalysisMode.STEPWISE
    ) -> int:
        """Estimate total token usage for analysis.
        
        Args:
            tasks: List of tasks to estimate (None for all)
            mode: Analysis mode
            
        Returns:
            Estimated total tokens
        """
        if tasks is None:
            tasks = list(TaskType)
        
        total = 0
        
        if mode == AnalysisMode.UNIFIED:
            # All tasks in one prompt
            for task in tasks:
                if task in self.task_prompts:
                    total += self.task_prompts[task].estimated_tokens
            # Add overhead for unified format
            total = int(total * 0.8)  # Some efficiency from combination
        else:
            # Each task separately
            for task in tasks:
                if task in self.task_prompts:
                    total += self.task_prompts[task].estimated_tokens
        
        return total
    
    def validate_output(
        self,
        task_type: TaskType,
        output: Any
    ) -> Tuple[bool, List[str]]:
        """Validate output against expected schema.
        
        Args:
            task_type: Task type to validate against
            output: Output to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if task_type not in self.task_prompts:
            return False, ["Unknown task type"]
        
        errors = []
        schema = self.task_prompts[task_type].output_schema
        
        # Basic schema validation
        if not isinstance(output, dict):
            errors.append("Output must be a dictionary")
            return False, errors
        
        # Check required fields
        for field, field_type in schema.items():
            if field not in output:
                errors.append(f"Missing required field: {field}")
            elif field_type == "array" and not isinstance(output[field], list):
                errors.append(f"Field {field} must be an array")
            elif field_type == "object" and not isinstance(output[field], dict):
                errors.append(f"Field {field} must be an object")
        
        return len(errors) == 0, errors
