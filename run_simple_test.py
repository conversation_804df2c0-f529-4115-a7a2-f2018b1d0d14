#!/usr/bin/env python3
"""
Simple test runner that doesn't hang.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def run_command_with_timeout(cmd, timeout=30):
    """Run a command with timeout."""
    try:
        print(f"Running: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"STDERR:\n{result.stderr}")
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"Command timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def test_basic_example():
    """Test basic example."""
    print("\n=== Testing Basic Example ===")
    return run_command_with_timeout("python examples.py --example basic_analysis --mock", 30)

def test_prompt_testing():
    """Test prompt testing."""
    print("\n=== Testing Prompt Testing ===")
    return run_command_with_timeout("python examples.py --example prompt_testing --mock", 45)

def test_quick_test():
    """Test quick test script."""
    print("\n=== Testing Quick Test ===")
    return run_command_with_timeout("python quick_test.py", 30)

def test_enhancements():
    """Test enhancements."""
    print("\n=== Testing Enhancements ===")
    return run_command_with_timeout("python test_enhancements.py", 45)

if __name__ == "__main__":
    print("Starting simple test runner...")
    
    tests = [
        ("Basic Example", test_basic_example),
        ("Prompt Testing", test_prompt_testing),
        ("Quick Test", test_quick_test),
        ("Enhancements", test_enhancements)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        result = test_func()
        results.append((test_name, result))
        
        print(f"Result: {'PASS' if result else 'FAIL'}")
        
        # Small delay between tests
        time.sleep(2)
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed")
