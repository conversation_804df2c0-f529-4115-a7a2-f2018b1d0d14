"""
Simplified script to validate system enhancements.
"""
import os
import json
import time
from pathlib import Path

# Set mock mode for testing
os.environ["USE_MOCK_LLM"] = "TRUE"

# Import components
from src.core.cache_manager import CacheManager
from src.core.api_monitor import APIMonitor

def test_cache_manager():
    """Test the cache manager functionality."""
    print("\n===== TESTING CACHE MANAGER =====")
    
    # Create cache directory
    cache_dir = "cache/modular"
    os.makedirs(cache_dir, exist_ok=True)
    
    # Initialize cache manager
    cache = CacheManager(cache_dir=cache_dir)
    
    # Test data
    doc_id = "test001"
    text = "This is a test document"
    mode = "stepwise"
    tasks = ["actors_relationships", "portrayals"]
    result = {
        "doc_id": doc_id,
        "quality_score": 0.75,
        "task_results": {
            "actors_relationships": {
                "actors": [
                    {"name": "Test Actor", "stakeholder_category": "Organization"}
                ]
            }
        },
        "warnings": ["Test warning"]
    }
    
    # Save to cache
    print("Saving result to cache...")
    cache.save_to_cache(doc_id, text, mode, tasks, result)
    
    # Check if cache file was created
    cache_files = list(Path(cache_dir).glob("*.json"))
    print(f"Cache files found: {len(cache_files)}")
    
    # Retrieve from cache
    print("Retrieving result from cache...")
    cached_result = cache.get_cached_result(doc_id, text, mode, tasks)
    
    if cached_result:
        print("Cache retrieval successful")
        print(f"Cached quality score: {cached_result.get('quality_score')}")
        return True
    else:
        print("Cache retrieval failed")
        return False

def test_api_monitor():
    """Test the API monitor functionality."""
    print("\n===== TESTING API MONITOR =====")
    
    # Create logs directory
    os.makedirs("logs/api_stats", exist_ok=True)
    
    # Initialize API monitor
    monitor = APIMonitor(window_minutes=5, log_dir="logs/api_stats")
    
    # Simulate some API requests
    print("Simulating API requests...")
    endpoints = ["/analyze/document", "/analyze/modular", "/health"]
    
    for i in range(10):
        endpoint = endpoints[i % len(endpoints)]
        duration = 0.2 + (i % 5) * 0.1
        status = 200 if i % 7 != 0 else 500
        
        monitor.record_request(endpoint, duration, status)
        time.sleep(0.1)
    
    # Get current stats
    stats = monitor.get_current_stats()
    print("\nAPI Monitor Statistics:")
    print(f"Total requests: {stats['total_requests']}")
    print(f"Average response time: {stats['avg_response_time']:.2f}s")
    print(f"Success rate: {stats['success_rate']*100:.1f}%")
    
    # Check alerts
    alerts = monitor.get_alerts()
    print("\nAlerts detected:", len(alerts))
    
    # Test successful if stats were generated
    return stats['total_requests'] > 0

def test_quality_warnings():
    """Test the enhanced quality warnings system."""
    print("\n===== TESTING QUALITY WARNINGS =====")
    
    # Sample quality scores and expected warnings
    test_cases = [
        (0.2, "Critical quality issue"),
        (0.4, "Low quality"),
        (0.6, "Moderate quality"),
        (0.8, "")
    ]
    
    results = []
    for score, expected in test_cases:
        # Create a simple function to mimic the warning generation
        def generate_warning(quality_score):
            if quality_score < 0.3:
                return ["Critical quality issue: Below 0.3 - Results need review"]
            elif quality_score < 0.5:
                return ["Low quality: Between 0.3-0.5 - May need improvement"]
            elif quality_score < 0.7:
                return ["Moderate quality: Between 0.5-0.7 - Consider refinements"]
            return []
        
        warnings = generate_warning(score)
        warning_text = warnings[0] if warnings else ""
        
        print(f"Score {score:.2f} -> {warning_text}")
        results.append(expected in warning_text)
    
    # All tests should pass
    return all(results)

if __name__ == "__main__":
    print("===== VALIDATING SYSTEM ENHANCEMENTS =====")
    
    # Create necessary directories
    os.makedirs("cache/modular", exist_ok=True)
    os.makedirs("logs/api_stats", exist_ok=True)
    
    # Run tests
    cache_test = test_cache_manager()
    api_test = test_api_monitor()
    quality_test = test_quality_warnings()
    
    # Print results
    print("\n===== TEST RESULTS =====")
    print(f"Cache Manager Test: {'PASSED' if cache_test else 'FAILED'}")
    print(f"API Monitor Test: {'PASSED' if api_test else 'FAILED'}")
    print(f"Quality Warnings Test: {'PASSED' if quality_test else 'FAILED'}")
    
    if cache_test and api_test and quality_test:
        print("\nAll enhancement components are working correctly!")
    else:
        print("\nSome components failed. Check the logs for details.")
    
    # Clean up environment
    os.environ.pop("USE_MOCK_LLM", None)
