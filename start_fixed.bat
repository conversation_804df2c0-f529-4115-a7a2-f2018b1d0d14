@echo off
chcp 65001 >nul
echo === Document Analysis Workflow Launcher ===
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR - Python not installed or not in PATH
    pause
    exit /b 1
)

echo OK - Python installed
echo.

REM Start workflow
echo Starting document analysis workflow...
echo Please wait, browser will open automatically...
echo.

python start_workflow.py

pause