openapi: 3.0.0
info:
  title: Document Analysis System API
  description: RESTful API for document analysis and prompt testing
  version: 1.0.0

servers:
  - url: http://localhost:8006
    description: Local development server

paths:
  /:
    get:
      summary: Root endpoint
      description: Get API information
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  version:
                    type: string
                  docs:
                    type: string
                  health:
                    type: string

  /health:
    get:
      summary: Health check
      description: Check system health
      responses:
        '200':
          description: Health status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  timestamp:
                    type: string
                  version:
                    type: string
                  config_loaded:
                    type: boolean
                  llm_ready:
                    type: boolean

  /analyze:
    post:
      summary: Analyze document
      description: Analyze a single document
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
      responses:
        '200':
          description: Analysis result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'

  /analyze/upload:
    post:
      summary: Analyze uploaded file
      description: Analyze an uploaded file
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                template_name:
                  type: string
                  default: unified_analysis
                doc_id:
                  type: string
      responses:
        '200':
          description: Analysis result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'

  /batch:
    post:
      summary: Batch analysis
      description: Analyze multiple documents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchAnalysisRequest'
      responses:
        '200':
          description: Batch analysis results
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentResponse'

  /test-prompt:
    post:
      summary: Test prompt template
      description: Test a prompt template
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PromptTestRequest'
      responses:
        '200':
          description: Test result
          content:
            application/json:
              schema:
                type: object
                properties:
                  test_id:
                    type: string
                  template_name:
                    type: string
                  document_id:
                    type: string
                  parse_success:
                    type: boolean
                  execution_time:
                    type: number
                  quality_score:
                    type: number
                  error_message:
                    type: string
                  parsed_result:
                    type: object

  /templates:
    get:
      summary: Get templates
      description: Get available prompt templates
      responses:
        '200':
          description: Template list
          content:
            application/json:
              schema:
                type: object
                properties:
                  templates:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        description:
                          type: string
                        complexity:
                          type: string
                        estimated_tokens:
                          type: integer
                        best_for:
                          type: string
                        variables:
                          type: object

  /templates/{template_name}:
    get:
      summary: Get template
      description: Get specific prompt template
      parameters:
        - name: template_name
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Template details
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                  description:
                    type: string
                  template:
                    type: string
                  complexity:
                    type: string
                  estimated_tokens:
                    type: integer
                  best_for:
                    type: string
                  variables:
                    type: object

components:
  schemas:
    DocumentRequest:
      type: object
      required:
        - doc_id
        - text
        - template_name
      properties:
        doc_id:
          type: string
        title:
          type: string
        text:
          type: string
        known_actors:
          type: array
          items:
            type: string
        template_name:
          type: string
          default: unified_analysis

    DocumentResponse:
      type: object
      properties:
        doc_id:
          type: string
        title:
          type: string
        analysis:
          type: object
        quality_score:
          type: number
        execution_time:
          type: number
        confidence_scores:
          type: object
        warnings:
          type: array
          items:
            type: string
        metadata:
          type: object

    PromptTestRequest:
      type: object
      required:
        - template_name
        - document_id
        - document_text
      properties:
        template_name:
          type: string
        document_id:
          type: string
        document_text:
          type: string
        document_title:
          type: string
        variables:
          type: object

    BatchAnalysisRequest:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          items:
            $ref: '#/components/schemas/DocumentRequest'
        template_name:
          type: string
          default: unified_analysis