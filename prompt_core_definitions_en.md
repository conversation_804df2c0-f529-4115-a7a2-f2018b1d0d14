# Core Definitions & Rules for Document Analysis

**Note:** This file contains the shared definitions, requirements, and rules. It should be included or referenced by all task-specific prompts.

## CRITICAL DEFINITIONS

### Actors
- **Definition**: Real persons or organizations only. No abstract/virtual roles.
- **Examples**: "FTC", "OpenAI", "Microsoft", "<PERSON>", "EU Commission"
- **NOT actors**: "policymakers" (unless quoted verbatim), "society", "the public", "future generations"
- **Edge cases**: 
  - Government entities count as actors (e.g., "Department of Justice")
  - Coalitions/alliances with specific names are actors (e.g., "Partnership on AI")
  - Anonymous sources are NOT actors unless they represent a named organization

### Relationships
- **Definition**: Directed/undirected interaction between two actors
- **Common types**: "regulates", "lobbies", "collaborates with", "funds", "criticizes", "competes with", "supports", "opposes", "sues", "partners with"
- **Direction matters**: "A regulates B" ≠ "B regulates A"
- **Evidence requirement**: Must have explicit text support, not inferred

### Portrayals

#### Hero
- **Definition**: Actor self-frames (or is framed) as able to fix problems
- **Key indicators**: 
  - Self-promotional language
  - Emphasizing protective/solution role
  - "We will save...", "Our solution protects..."
- **Example**: "OpenAI positions itself as the responsible AI leader ensuring safe AGI development"

#### Victim  
- **Definition**: Actor framed as harmed or bearing consequences from narratives/actions
- **Key requirement**: Harm from ACTIONS/NARRATIVES, not just policy limitations
- **Key indicators**:
  - "unfairly targeted", "bearing the burden", "suffering from"
- **Example**: "Small startups claim they are being crushed by big tech's regulatory capture"

#### Devil
- **Definition**: Opponents framed as malicious or more evil than they are
- **Key indicators**:
  - Exaggerating motives/harms
  - Using loaded language ("reckless", "dangerous", "irresponsible")
  - Attributing malice without evidence
- **Example**: "Critics paint tech companies as deliberately addicting children for profit"

### Issue Scope Strategies

#### Issue Expansion
- **Definition**: Deliberate broadening of scope/audience
- **Mechanism**: Diffusing costs across many while benefits concentrate to few
- **Key indicators**:
  - Appeals to general public concern
  - "This affects everyone...", "National security issue..."
  - Invoking children, safety, democracy
- **Example**: "Framing AI regulation as essential for protecting all workers' jobs"

#### Issue Containment  
- **Definition**: Deliberate narrowing of scope/audience to specialists
- **Mechanism**: Limiting public salience, keeping debate technical
- **Key indicators**:
  - "This is a technical matter...", "Experts agree..."
  - Using jargon to exclude lay audience
  - Referring to specialized committees/processes
- **Example**: "Arguing that AI safety should be left to technical standards bodies"

### Causal Mechanisms

#### Intentional
- **Definition**: Assigns blame deliberately to harm others' reputations or shift responsibility
- **Key indicators**:
  - "X deliberately...", "X's reckless actions caused..."
  - Clear attribution of fault
  - Moral responsibility language
- **Example**: "The crisis was caused by Meta's deliberate neglect of safety measures"

#### Inadvertent
- **Definition**: Attributes problems to unintended consequences of a policy
- **Key indicators**:
  - "Unintended consequences", "Despite good intentions..."
  - "The policy inadvertently led to..."
  - No moral blame assigned
- **Example**: "GDPR inadvertently strengthened big tech's market position"

### Stakeholder Categories
1. **Think Tanks**: Policy research organizations (e.g., "Brookings", "RAND")
2. **Government**: Official government entities (e.g., "FTC", "White House", "EU Commission")
3. **Media**: News organizations and journalists (e.g., "NYT", "Reuters")
4. **Corporate**: Companies and business entities (e.g., "Google", "OpenAI")
5. **NGOs and Professional Organizations**: Non-profits and associations (e.g., "EFF", "IEEE")
6. **Universities and Research Institutes**: Academic institutions (e.g., "MIT", "Stanford AI Lab")
7. **Political Entities**: Political parties, campaigns, PACs (e.g., "Democratic Party", "Senator X")
8. **Consultants and Analysts**: Individual experts or firms (e.g., "McKinsey", "Gartner")
9. **Legal and Industry-specific Bodies**: Regulatory and standards bodies (e.g., "ISO", "Bar Association")
10. **State Guidelines and Documents**: Official policy documents (when personified as actors)
11. **Others**: Anything not fitting above categories

## EXTRACTION REQUIREMENTS

### Specificity Rules
- Extract specific, named actors (e.g., "FTC", "OpenAI", "NYT")
- Avoid categories like "policymakers" unless quoted verbatim from source
- When document says "tech companies", identify which specific companies if mentioned
- Use the most specific name available ("FTC" not "government regulators")

### Relationship Rules  
- Maintain directional consistency for one-way relationships
- Split multi-actor mentions into multiple pairwise rows
- Use concise verb phrases (2-3 words typically)
- Relationships must be explicit in text, not inferred
- Example transformations:
  - "A, B, and C collaborate" → "A collaborates with B", "A collaborates with C", "B collaborates with C"
  - "A regulates B and C" → "A regulates B", "A regulates C"

### Evidence Standards
- Must be direct quotations OR tight paraphrases
- Must be traceable to exact text spans
- Include enough context to understand the claim
- Maximum 2-3 sentences for evidence
- If paraphrasing, stay extremely close to original language

### Handling Empty Sections
- If a section has no valid findings, set `skipped=true`
- Provide clear `skip_reason` explaining why
- Common skip reasons:
  - "No narrative framing present - only factual description"
  - "No named actors identified"
  - "Hypothetical scenarios only"

## EXCLUSION RULES

### Do NOT Extract

1. **Non-real/virtual roles**
   - Hypothetical actors ("a potential regulator")
   - Generic categories without specific names
   - Future/imagined entities

2. **Hypothetical/suggestive statements**
   - "Should", "could", "might" without realized framing
   - Policy recommendations not yet implemented
   - Speculative scenarios

3. **Pure factual descriptions**
   - Flat descriptions of current policy
   - Technical specifications without narrative
   - Historical facts without framing

4. **Unsupported claims**
   - Generic concerns without named actors
   - Indirect/implicit framings not grounded in text
   - Inferences beyond what text explicitly states

5. **Analyst opinions**
   - Pure insights/opinions from document author
   - Analysis not attributed to specific actors
   - Academic commentary without narrative framing

6. **Non-narrative content**
   - Simple disadvantages/advantages lists
   - Cost-benefit analyses without blame/credit
   - Technical trade-offs without moral framing

## SPECIAL HANDLING INSTRUCTIONS

### Multi-document Consistency
- Maintain consistent actor naming across documents
- Use standard abbreviations (e.g., always "FTC" not "Federal Trade Commission")
- Track actor name variations and consolidate

### AI Decision Rules
- Only suggest `add_actor` when text clearly introduces new relevant actor
- Only suggest `remove_actor` for clear errors or duplicates
- Default to `none` when no changes needed
- Provide clear reasoning for any suggested changes

### Edge Cases

1. **Collective actors**: When document mentions "Google, Microsoft, and Meta", extract each separately
2. **Subsidiaries**: Treat as separate actors from parent (e.g., "DeepMind" separate from "Google")
3. **Government agencies**: Each agency is separate actor ("FTC" ≠ "DOJ")
4. **International entities**: Include country prefix when relevant ("UK Competition Authority")
5. **Temporal changes**: If actor changes name/status, use current name in document
6. **Anonymous sources**: Only include if they represent named organization ("A Google spokesperson")

## OUTPUT QUALITY CHECKLIST

- [ ] All actors are real, named entities
- [ ] All relationships have text evidence
- [ ] Evidence quotes are accurate
- [ ] Enum values match schema exactly
- [ ] No hypothetical or speculative content
- [ ] No unsupported inferences
- [ ] Consistent actor naming throughout
- [ ] Appropriate use of `skipped` when no findings
- [ ] AI decisions have clear reasoning
