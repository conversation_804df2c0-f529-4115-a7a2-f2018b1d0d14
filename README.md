# Document Analysis System - Python Implementation

A comprehensive Python-based document analysis system that extracts actors, relationships, and narrative patterns from text documents using Large Language Models (LLMs).

## Features

- **Document Analysis**: Extract actors, relationships, and narrative patterns
- **Multiple LLM Support**: OpenAI GPT and Anthropic Claude models
- **Prompt Testing Framework**: Test and evaluate different prompt templates
- **Batch Processing**: Process multiple documents efficiently
- **Quality Assessment**: Validate analysis results
- **Flexible Configuration**: Extensive configuration options
- **Multiple File Formats**: Support for TXT, PDF, DOCX, CSV, JSON, MD, HTML

### NEW: Modular Analysis System

- **Modular Task Execution**: Break down analysis into independent, reusable tasks
- **Multiple Analysis Modes**:
  - **Unified Mode**: All tasks in a single prompt for quick comprehensive analysis
  - **Stepwise Mode**: Sequential task execution with dependency management
  - **Selective Mode**: Run only specific tasks based on requirements
- **Task Dependencies**: Automatic resolution of task dependencies
- **Bilingual Support**: Full support for English and Chinese prompts
- **Quality Scoring**: Per-task quality assessment and warnings
- **Task-Specific Prompts**: Optimized prompts for each analysis task

### NEW: System Enhancements (August 2025)

- **Enhanced Chinese Prompts**: Optimized Chinese prompt templates to ensure reliable JSON parsing
- **Granular Quality Assessment**: More detailed quality scoring thresholds with targeted warnings:
  - Below 0.3: Critical quality issue, needs review
  - 0.3–0.5: Low quality, may need improvement
  - 0.5–0.7: Moderate quality, consider refinements
  - Above 0.7: Good quality
- **Result Caching System**: Cache analysis results to avoid redundant LLM calls
  - MD5-based cache keys using document content and parameters
  - Configurable cache expiration (default 24 hours)
  - Only caches results with quality scores ≥ 0.3
- **API Performance Monitoring**: Track and analyze API request metrics
  - Request counts, durations, status codes
  - Endpoint-specific statistics
  - Rolling window metrics with periodic logging
  - Automated alerts for high latency or low success rates
- **LLM Response Visualization and Statistics**: Analyze and visualize LLM response data
  - Quality score distribution with quality tiers
  - Average quality score by task type comparison
  - Response time distribution analysis
  - Quality comparison between languages (EN vs ZH)
  - Quality score trends over time
  - Command-line interface for generating visualizations
  - Automatic response data collection and storage

## Installation

### Prerequisites

- Python 3.8+
- pip package manager

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Environment Setup

Copy the environment template and configure your API keys:

```bash
cp .env.template .env
```

Then edit the `.env` file with your configuration:

```env
# OpenAI API Key (for GPT models)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LLM Provider (openai or anthropic)
LLM_PROVIDER=openai

# LLM Model
LLM_MODEL=gpt-4

# Use mock LLM for testing (true/false)
USE_MOCK_LLM=true

# LLM Settings
LLM_TEMPERATURE=0
LLM_MAX_TOKENS=4000
```

## Quick Start

### Basic Analysis

```bash
# Run basic analysis example
python examples.py --example basic_analysis

# Analyze a specific document
python examples.py --example basic_analysis --document path/to/document.txt

# Use mock LLM for testing
python examples.py --example basic_analysis --mock
```

### Prompt Testing

```bash
# Test all prompt templates
python examples.py --example prompt_testing

# Test with specific document
python examples.py --example prompt_testing --document path/to/document.txt

# Test with directory of documents
python examples.py --example prompt_testing --directory path/to/documents/
```

### Batch Analysis

```bash
# Analyze multiple documents
python examples.py --example batch_analysis --directory path/to/documents/

# Save results to specific directory
python examples.py --example batch_analysis --directory path/to/documents/ --output results/
```

### Web API

Start the web API server:

```bash
python web_api.py
```

The API will be available at `http://localhost:8006` with interactive documentation at `http://localhost:8006/docs`.

**Key API Endpoints:**
- `GET /` - API information
- `GET /health` - Health check
- `POST /analyze` - Analyze a document
- `POST /analyze/modular` - Analyze document using modular approach
- `GET /analyze/modes` - Get available analysis modes and tasks
- `POST /analyze/upload` - Analyze uploaded file
- `POST /batch` - Batch analyze documents
- `POST /test-prompt` - Test prompt templates
- `GET /templates` - Get available templates
- `GET /metrics` - Get system metrics

## Configuration

### Create Configuration File

```python
from src.core.config import Config

# Create configuration
config = Config()

# Modify settings
config.llm.provider = "openai"
config.llm.model = "gpt-4"
config.llm.temperature = 0.1

config.analysis.min_confidence_score = 0.8
config.analysis.max_document_length = 5000

# Save configuration
config.save_to_file("my_config.json")
```

### Load Configuration

```python
from src.core.config import Config

# Load from file
config = Config.from_file("my_config.json")
```

## Usage Examples

### Basic Document Analysis

```python
from src.core.config import Config
from src.core.analyzer import DocumentAnalyzer
from src.utils.document_reader import DocumentReader, create_sample_document

# Create configuration
config = Config()
config.use_mock_llm = True  # Use mock for testing

# Get document
document = create_sample_document()

# Create analyzer
analyzer = DocumentAnalyzer(config)

# Analyze document
result = analyzer.analyze_document(document, "unified_analysis")

# Display results
print(f"Quality Score: {result.quality_score:.2f}")
print(f"Actors Found: {len(result.analysis.get('actors', []))}")
print(f"Relationships Found: {len(result.analysis.get('relationships', {}).get('items', []))}")
```

### Modular Document Analysis

```python
from src.core.config import Config
from src.core.modular_analyzer import ModularDocumentAnalyzer
from src.core.modular_prompts import AnalysisMode, TaskType
from src.utils.document_reader import create_sample_document

# Create configuration
config = Config()

# Create modular analyzer
analyzer = ModularDocumentAnalyzer(config)

# Get document
document = create_sample_document()

# 1. Unified mode - all tasks in one prompt
result = analyzer.analyze_modular(
    document,
    mode=AnalysisMode.UNIFIED
)

# 2. Stepwise mode - sequential task execution
result = analyzer.analyze_modular(
    document,
    mode=AnalysisMode.STEPWISE
)

# 3. Selective mode - specific tasks only
result = analyzer.analyze_modular(
    document,
    mode=AnalysisMode.SELECTIVE,
    tasks=[TaskType.ACTORS_RELATIONSHIPS, TaskType.PORTRAYALS]
)

# 4. Chinese language analysis
analyzer.language = "zh"
result = analyzer.analyze_modular(
    document,
    mode=AnalysisMode.STEPWISE
)

# Display results
print(f"Mode: {result.mode.value}")
print(f"Quality Score: {result.quality_score:.2f}")
for task, timing in result.task_timings.items():
    print(f"{task.value}: {timing:.2f}s")
```

### Prompt Testing

```python
from src.core.config import Config
from src.testing.prompt_tester import PromptTester
from src.utils.document_reader import create_sample_document

# Create configuration
config = Config()
config.use_mock_llm = True

# Create tester
tester = PromptTester(config)

# Get document
document = create_sample_document()

# Test all templates
results = tester.test_all_templates(document)

# Generate report
report_dir = tester.generate_report()
print(f"Report generated in: {report_dir}")
```

### Batch Processing

```python
from src.core.config import Config
from src.core.analyzer import DocumentAnalyzer
from src.utils.document_reader import DocumentReader

# Create configuration
config = Config()
config.use_mock_llm = True

# Read documents
reader = DocumentReader()
documents = reader.read_directory("path/to/documents/")

# Create analyzer
analyzer = DocumentAnalyzer(config)

# Analyze documents
results = analyzer.analyze_documents_batch(documents)

# Save results
output_file = analyzer.save_batch_results(results)
print(f"Results saved to: {output_file}")
```

## Prompt Templates

The system includes various prompt templates optimized for different use cases:

### Core Analysis Templates
- `unified_analysis`: Comprehensive document analysis
- `actor_extraction`: Extract actors and categorize them
- `relationship_analysis`: Analyze relationships between actors

### Narrative Analysis Templates
- `portrayal_analysis`: Identify hero/victim/devil portrayals
- `issue_scope_analysis`: Detect issue expansion/containment strategies
- `causal_mechanism_analysis`: Identify intentional/inadvertent causal mechanisms

### Quality Assessment Templates
- `quality_assessment`: Assess analysis quality and completeness
- `validation_check`: Validate analysis against original document

### Provider-Optimized Templates
- `openai_optimized`: Optimized for OpenAI models
- `anthropic_optimized`: Optimized for Anthropic models
- `zhipu_optimized`: Chinese-optimized for Zhipu models

## File Format Support

The system supports multiple file formats:

- **TXT**: Plain text files
- **PDF**: PDF documents (using pdfplumber)
- **DOCX**: Word documents (using python-docx)
- **CSV**: CSV files (converted to structured text)
- **JSON**: JSON files (converted to formatted text)
- **MD**: Markdown files
- **HTML**: HTML files

## Output Schema

The system produces structured JSON output with the following components:

```json
{
  "doc_id": "string",
  "title": "string|null",
  "actors": [
    {
      "name": "string",
      "stakeholder_category": "string",
      "notes": "string|null"
    }
  ],
  "relationships": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "a_name": "string",
        "b_name": "string",
        "relationship": "string",
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null",
        "a_type": "string",
        "b_type": "string",
        "evidence": "string"
      }
    ]
  },
  "portrayals": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "string",
        "explanation": "string"
      }
    ],
    "shifts": {
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean",
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }
  },
  "issue_scope": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Issue expansion|Issue containment",
        "evidence": "string",
        "explanation": "string"
      }
    ]
  },
  "causal_mechanisms": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Intentional|Inadvertent",
        "evidence": "string",
        "explanation": "string"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}
```

## Project Structure

```
document-analysis-python/
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py          # Configuration management
│   │   ├── analyzer.py        # Main document analyzer
│   │   ├── prompts.py         # Prompt templates
│   │   ├── modular_analyzer.py # Modular document analyzer
│   │   └── modular_prompts.py  # Modular prompt system
│   ├── utils/
│   │   ├── __init__.py
│   │   └── document_reader.py # Document reading utilities
│   └── testing/
│       ├── __init__.py
│       └── prompt_tester.py   # Prompt testing framework
├── examples.py                # Usage examples
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## Testing

The system includes comprehensive testing capabilities:

### Unit Tests

```bash
# Run all tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=src

# Run specific test file
python -m pytest tests/test_analyzer.py
```

### Prompt Testing

```bash
# Test all prompts with sample documents
python examples.py --example prompt_testing

# Test specific prompts
python examples.py --example prompt_testing --document sample.txt
```

### Integration Tests

```bash
# Test full pipeline
python examples.py --example batch_analysis --directory test_data/
```

## Configuration Options

### LLM Configuration

- `provider`: LLM provider (openai, anthropic)
- `model`: Model name (gpt-4, claude-3-sonnet, etc.)
- `api_key`: API key for the provider
- `temperature`: Response randomness (0.0 to 1.0)
- `max_tokens`: Maximum response length
- `timeout`: Request timeout in seconds
- `max_retries`: Maximum retry attempts

### Analysis Configuration

- `stakeholder_categories`: List of stakeholder categories
- `enable_actor_extraction`: Enable actor extraction
- `enable_relationship_analysis`: Enable relationship analysis
- `enable_portrayal_analysis`: Enable portrayal analysis
- `enable_issue_scope_analysis`: Enable issue scope analysis
- `enable_causal_mechanism_analysis`: Enable causal mechanism analysis
- `min_confidence_score`: Minimum confidence threshold
- `max_document_length`: Maximum document length
- `chunk_size`: Text chunk size for processing

### Output Configuration

- `include_raw_text`: Include original text in output
- `include_confidence_scores`: Include confidence scores
- `include_quality_metrics`: Include quality metrics
- `format_schema_validation`: Validate output schema
- `pretty_print`: Format JSON output

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Ensure your `.env` file contains the correct API keys
   - Check that the API keys are valid and have sufficient permissions

2. **Document Reading Errors**
   - Ensure the document file exists and is readable
   - Check that the file format is supported
   - Verify file encoding (try UTF-8)

3. **JSON Parsing Errors**
   - The LLM response may not be valid JSON
   - Check the response text for formatting issues
   - Try adjusting the prompt temperature

4. **Memory Issues**
   - Reduce `max_document_length` in configuration
   - Process documents in smaller batches
   - Use chunking for large documents

### Debug Mode

Enable debug mode for detailed logging:

```python
config = Config()
config.debug_mode = True
```

### Mock LLM

Use the mock LLM for testing without API calls:

```python
config = Config()
config.use_mock_llm = True
```

## License

MIT License

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review the examples in `examples.py`
- Examine the test files for usage patterns