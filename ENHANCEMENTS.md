# 政策叙事分析系统增强功能

本文档详细介绍了为政策叙事文档分析系统实施的四项主要增强功能：中文提示优化、质量评分阈值调整、结果缓存系统和API性能监控。

## 1. 中文提示优化

为解决中文分析中的JSON解析问题，我们对中文提示模板进行了优化。

### 实施内容：

- 在所有中文提示模板中增加了明确的JSON格式要求
- 添加了不使用markdown代码块的明确指示
- 强调返回的内容必须是机器可直接解析的JSON
- 明确要求返回的内容以`{`开始，以`}`结束

### 示例（部分改进）：

```markdown
**重要提示：** 
- 你必须仅返回一个符合"输出模式"的有效JSON对象
- 不要使用markdown代码块（如```json）
- 不要包含任何解释性文字或前导文本
- 返回的JSON必须可由机器直接解析
- 返回的内容应当以{开始，以}结束
```

### 文件变更：

- `prompt_task1_actors_relationships_zh.md`
- `prompt_task2_portrayals_zh.md`
- `prompt_task3_issue_scope_zh.md`
- `prompt_task4_causal_mechanisms_zh.md`

## 2. 质量评分阈值调整

增加了更细致的质量评分阈值，提供更详细的质量反馈。

### 实施内容：

- 定义了四个质量等级：
  - <0.3: 严重质量问题（需要审查）
  - 0.3-0.5: 低质量（可能需要改进）
  - 0.5-0.7: 中等质量（建议细化）
  - >0.7: 良好质量
- 为每个质量等级添加了相应的警告信息
- 在`_generate_warnings`方法中实现了增强的质量评估

### 文件变更：

- `src/core/modular_analyzer.py`

## 3. 结果缓存系统

实现了缓存系统，避免对相同文档重复进行分析，提高系统性能。

### 实施内容：

- 创建了`CacheManager`类，管理分析结果的缓存
- 基于文档内容和分析参数的MD5哈希生成缓存键
- 配置的缓存过期时间（默认24小时）
- 只缓存质量分数≥0.3的结果
- 自动清理过期缓存条目

### 使用方式：

```python
from src.core.modular_analyzer import ModularDocumentAnalyzer

# 默认启用缓存
analyzer = ModularDocumentAnalyzer(config, use_cache=True)

# 分析结果会自动缓存
result = analyzer.analyze(doc_id="123", text="文档内容", mode="stepwise")

# 再次分析相同文档将从缓存中获取结果
cached_result = analyzer.analyze(doc_id="123", text="文档内容", mode="stepwise")
```

### 文件变更：

- 新增: `src/core/cache_manager.py`
- 修改: `src/core/modular_analyzer.py`

## 4. API性能监控

实现了API性能监控系统，跟踪API性能指标并生成警报。

### 实施内容：

- 创建了`APIMonitor`类，用于监控API性能
- 实现功能：
  - 跟踪API请求、响应时间和状态码
  - 按端点收集指标
  - 计算成功率、平均响应时间、P95响应时间
  - 定期保存性能日志（每5分钟）
  - 生成性能警报（响应时间过长、成功率过低）

### 性能指标：

- 总请求数和每分钟请求数
- 平均响应时间
- P95响应时间（95%的请求响应时间低于此值）
- 成功率（状态码200-299的请求比例）
- 按端点细分的指标

### 文件变更：

- 新增: `src/core/api_monitor.py`
- 修改: `web_api.py`

## 安装和使用说明

1. 所有新功能已完全集成到现有系统中
2. 缓存文件保存在`cache/modular/`目录下
3. API性能日志保存在`logs/api_stats/`目录下
4. 默认启用所有增强功能，无需额外配置

## 5. LLM响应结果可视化与统计

此增强功能为系统添加了LLM响应结果的可视化和统计分析能力，帮助用户直观了解模型性能和输出质量。

### 设计思路

- 收集LLM响应数据，包括质量分数、响应时间、语言和任务类型等信息
- 提供多种可视化图表，直观展示质量分布和性能指标
- 生成详细的统计报告，便于分析模型表现
- 支持中英文双语分析结果对比

### 实现细节

#### 响应收集器

- 创建了`ResponseCollector`类，用于收集和存储LLM响应数据
- 支持单条和批量响应存储
- 自动为每个响应生成唯一ID
- 提供响应文件管理功能，包括清理过期数据

```python
# 示例用法
from src.core.response_collector import ResponseCollector

collector = ResponseCollector(storage_dir="results/llm_responses")
collector.save_response(response_data)
```

#### 可视化分析器

- 创建了`LLMResponseAnalyzer`类，用于分析和可视化LLM响应数据
- 提供多种可视化图表:
  - 质量分数分布图: 展示四个质量等级分布情况
  - 任务质量对比图: 比较不同任务的平均质量分数
  - 响应时间分布图: 分析LLM响应时间分布
  - 语言对比图: 对比中英文分析结果的质量差异
  - 质量趋势图: 展示质量分数随时间的变化趋势

```python
# 示例用法
from src.visualization.llm_stats import LLMResponseAnalyzer

analyzer = LLMResponseAnalyzer()
analyzer.load_response_data()
analyzer.plot_quality_distribution()
```

#### 命令行工具

- 创建了命令行脚本`visualize_llm_responses.py`，方便用户快速生成可视化报告
- 支持多种参数选项，可灵活选择所需的可视化类型
- 提供示例程序`response_visualization_example.py`，展示完整工作流程

```bash
# 命令行使用示例
python src/visualization/visualize_llm_responses.py --all
```

### 文件变更

- 新增: `src/visualization/llm_stats.py`
- 新增: `src/core/response_collector.py`
- 新增: `src/visualization/visualize_llm_responses.py`
- 新增: `src/visualization/response_visualization_example.py`

### 安装与配置

- 新功能依赖matplotlib和pandas库
- 默认将可视化结果保存在`results/visualizations/`目录
- 响应数据默认保存在`results/llm_responses/`目录

```bash
pip install matplotlib pandas
```

## 下一步建议

1. 进一步优化中文提示词，特别是针对`prompt_task2_portrayals_zh.md`和`prompt_task3_issue_scope_zh.md`
2. 增加缓存管理界面，便于查看和清理缓存
3. 创建API性能监控仪表板，实时展示系统状态
4. 考虑添加自动扩缩功能，根据API负载自动调整资源
5. 开发Web界面，更直观地展示可视化结果
