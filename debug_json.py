#!/usr/bin/env python3
"""
Debug JSON parsing issues.
"""

import re
import json

def debug_json_parsing():
    """Debug the JSON parsing step by step."""
    print("=== Debug JSON Parsing ===")
    
    test_cases = [
        '{"test": "value",}',
        "{'test': 'value'}",
        "{test: 'value'}",
        '{"test": "value", \'name\': "John"}'
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- Test Case {i+1}: {test_case} ---")
        
        # Step 1: Original
        print(f"Original: {test_case}")
        
        # Step 2: Fix trailing commas
        fixed = test_case
        fixed = re.sub(r',(\s*[}\]])', r'\1', fixed)
        print(f"After trailing comma fix: {fixed}")
        
        # Step 3: Fix missing quotes around keys
        fixed = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', fixed)
        print(f"After key quotes fix: {fixed}")
        
        # Step 4: Fix single quotes around values
        fixed = re.sub(r"'([^']*?)'", r'"\1"', fixed)
        print(f"After value quotes fix: {fixed}")
        
        # Step 5: Fix single quotes around keys
        fixed = re.sub(r'([{,]\s*)\'([a-zA-Z_][a-zA-Z0-9_]*)\'\s*:', r'\1"\2":', fixed)
        print(f"After key single quotes fix: {fixed}")
        
        # Try to parse
        try:
            result = json.loads(fixed)
            print(f"SUCCESS: {result}")
        except json.JSONDecodeError as e:
            print(f"FAILED: {e}")
            
            # Try aggressive fix
            try:
                fixed2 = re.sub(r'([{,]\s*)\'([a-zA-Z_][a-zA-Z0-9_]*)\'\s*:', r'\1"\2":', fixed)
                fixed2 = re.sub(r':\s*\'([^\']*?)\'', r': "\1"', fixed2)
                print(f"Aggressive fix: {fixed2}")
                result = json.loads(fixed2)
                print(f"SUCCESS (aggressive): {result}")
            except json.JSONDecodeError as e2:
                print(f"FAILED (aggressive): {e2}")

if __name__ == "__main__":
    debug_json_parsing()