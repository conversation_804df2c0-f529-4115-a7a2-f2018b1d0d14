# 任务二：角色描绘检测 (英雄/受害者/魔鬼)

**系统指令：** 你是一个叙事分析模型。请阅读输入文档和给定的定义。你的任务是识别所有英雄、受害者或魔鬼的角色描绘实例。请仅返回一个符合“输出模式”的有效JSON对象，不要包含任何解释性文字。

**引用定义：** 请遵守 `prompt_core_definitions_zh.md` 中的所有规则。

## 特定任务目标

-   检测被塑造为`英雄`、`受害者`或`魔鬼`的行动者。
-   为每种描绘提供直接证据和简要解释。

## 输出模式

```json
{
  "doc_id": "string",
  "portrayals": {
    "skipped": false,
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "引语",
        "explanation": "1-2句话解释为何是英雄/受害者/魔鬼"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}
```

## 输入参数

- `doc_id`: 字符串
- `title`: 字符串或null
- `text`: 字符串 (文档正文)
- `optional_known_actors`: 字符串数组
