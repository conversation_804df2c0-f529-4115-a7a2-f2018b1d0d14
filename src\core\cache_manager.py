"""
Cache manager for document analysis results.
"""
import json
import hashlib
import time
from pathlib import Path
from typing import Dict, Any, Optional
import logging

class CacheManager:
    """Manages caching of document analysis results to avoid repeated processing."""
    
    def __init__(self, cache_dir: str = "cache", max_age_hours: int = 24):
        """
        Initialize the cache manager.
        
        Args:
            cache_dir: Directory to store cache files
            max_age_hours: Maximum age of cache entries in hours
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True, parents=True)
        self.max_age_seconds = max_age_hours * 3600
        self.logger = logging.getLogger(__name__)
    
    def _generate_cache_key(self, doc_id: str, text: str, mode: str, tasks: list) -> str:
        """Generate a unique cache key based on document content and analysis parameters."""
        content = f"{doc_id}:{text}:{mode}:{','.join(sorted(tasks))}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the file path for a cache key."""
        return self.cache_dir / f"{cache_key}.json"
    
    def get_cached_result(self, doc_id: str, text: str, mode: str, tasks: list) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached result if available and not expired.
        
        Args:
            doc_id: Document ID
            text: Document text
            mode: Analysis mode
            tasks: List of analysis tasks
            
        Returns:
            Cached result or None if not found/expired
        """
        cache_key = self._generate_cache_key(doc_id, text, mode, tasks)
        cache_path = self._get_cache_path(cache_key)
        
        if not cache_path.exists():
            return None
        
        try:
            # Check if cache is expired
            if time.time() - cache_path.stat().st_mtime > self.max_age_seconds:
                self.logger.info(f"Cache expired for {doc_id}")
                return None
            
            # Load and return cached result
            with open(cache_path, 'r', encoding='utf-8') as f:
                result = json.load(f)
                self.logger.info(f"Cache hit for {doc_id}")
                return result
                
        except Exception as e:
            self.logger.error(f"Error reading cache: {e}")
            return None
    
    def save_to_cache(self, doc_id: str, text: str, mode: str, tasks: list, result: Dict[str, Any]) -> bool:
        """
        Save analysis result to cache.
        
        Args:
            doc_id: Document ID
            text: Document text
            mode: Analysis mode
            tasks: List of analysis tasks
            result: Analysis result to cache
            
        Returns:
            True if successfully cached, False otherwise
        """
        cache_key = self._generate_cache_key(doc_id, text, mode, tasks)
        cache_path = self._get_cache_path(cache_key)
        
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Saved to cache: {doc_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving to cache: {e}")
            return False
            
    def clear_expired_cache(self) -> int:
        """
        Clear expired cache entries.
        
        Returns:
            Number of entries cleared
        """
        cleared_count = 0
        for cache_file in self.cache_dir.glob("*.json"):
            if time.time() - cache_file.stat().st_mtime > self.max_age_seconds:
                cache_file.unlink()
                cleared_count += 1
                
        self.logger.info(f"Cleared {cleared_count} expired cache entries")
        return cleared_count
