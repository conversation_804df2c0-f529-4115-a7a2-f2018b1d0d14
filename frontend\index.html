<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档分析工作台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .workflow-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }
        .step.active .step-circle {
            background: #007bff;
            color: white;
        }
        .step.completed .step-circle {
            background: #28a745;
            color: white;
        }
        .document-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        .document-item {
            padding: 8px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .document-item:hover {
            background-color: #f8f9fa;
        }
        .document-item.selected {
            background-color: #007bff;
            color: white;
        }
        .prompt-editor {
            min-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .response-area {
            min-height: 300px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .analysis-result {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .loading.show {
            display: block;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .btn-step {
            min-width: 120px;
        }
        .template-card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .template-card:hover {
            background-color: #f8f9fa;
        }
        .template-card.selected {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-file-alt"></i> 文档分析工作台
        </h1>
        
        <!-- API配置状态面板 -->
        <div class="workflow-panel">
            <div class="row">
                <div class="col-md-8">
                    <h5><i class="fas fa-cog"></i> API配置状态</h5>
                    <div id="apiConfigStatus">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator status-pending" id="apiStatusIndicator"></span>
                            <span id="apiStatusText">正在检查API配置...</span>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">LLM提供商:</small>
                                <div id="llmProvider">-</div>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">模型:</small>
                                <div id="llmModel">-</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">API状态:</small>
                                <div id="apiMode">-</div>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">Base URL:</small>
                                <div id="baseUrl">-</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5><i class="fas fa-info-circle"></i> 系统信息</h5>
                    <div id="systemInfo">
                        <div class="mb-2">
                            <small class="text-muted">API版本:</small>
                            <div id="apiVersion">1.0.0</div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">服务状态:</small>
                            <div id="serviceStatus">
                                <span class="status-indicator status-pending"></span>
                                <span>检查中...</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">最后更新:</small>
                            <div id="lastUpdate">-</div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary" id="refreshConfigBtn" onclick="refreshApiConfig()">
                                <i class="fas fa-sync-alt"></i> 刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作流程指示器 -->
        <div class="workflow-panel">
            <div class="step-indicator">
                <div class="step active" id="step1">
                    <div class="step-circle">1</div>
                    <small>选择文档</small>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <small>编辑提示词</small>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <small>提交请求</small>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <small>分析结果</small>
                </div>
            </div>
        </div>

        <!-- 步骤1: 选择文档 -->
        <div class="workflow-panel" id="panel1">
            <h3><i class="fas fa-folder-open"></i> 选择文档</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>选择文件</h5>
                    <div class="mb-3">
                        <div class="input-group">
                            <input type="file" class="form-control" id="fileInput" accept=".txt,.pdf,.docx,.doc,.xlsx,.xls,.csv" multiple>
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-file-upload"></i> 浏览
                            </button>
                        </div>
                        <small class="text-muted">支持格式: TXT, PDF, DOC, DOCX, XLS, XLSX, CSV</small>
                    </div>
                    
                    <h5>已选文件</h5>
                    <div class="document-list" id="selectedFilesList">
                        <!-- 已选择的文件列表 -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>文档预览</h5>
                    <div class="response-area" id="documentPreview">
                        请选择一个文档进行预览
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-primary btn-step" onclick="nextStep(1)" id="nextStep1" disabled>
                    下一步 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>

        <!-- 步骤2: 编辑提示词 -->
        <div class="workflow-panel" id="panel2" style="display: none;">
            <h3><i class="fas fa-edit"></i> 编辑提示词</h3>
            <div class="row">
                <div class="col-md-4">
                    <h5>模板选择</h5>
                    <div id="templateList">
                        <!-- 模板列表将在这里动态加载 -->
                    </div>
                    <div class="mt-3">
                        <h6>提示词变量</h6>
                        <div id="variableInputs">
                            <!-- 变量输入将在这里动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <h5>提示词编辑器</h5>
                    <textarea id="promptEditor" class="form-control prompt-editor" placeholder="在这里编辑提示词..."></textarea>
                    <div class="mt-2">
                        <small class="text-muted">
                            提示词长度: <span id="promptLength">0</span> 字符
                        </small>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-secondary btn-step" onclick="previousStep(2)">
                    <i class="fas fa-arrow-left"></i> 上一步
                </button>
                <button class="btn btn-primary btn-step" onclick="nextStep(2)" id="nextStep2">
                    下一步 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>

        <!-- 步骤3: 提交请求 -->
        <div class="workflow-panel" id="panel3" style="display: none;">
            <h3><i class="fas fa-paper-plane"></i> 提交API请求</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>请求配置</h5>
                    <div class="mb-3">
                        <label class="form-label">API端点</label>
                        <select class="form-select" id="apiEndpoint">
                            <option value="analyze">单文档分析（文本）</option>
                            <option value="analyze/upload">文件上传分析</option>
                            <option value="batch">批量分析</option>
                            <option value="test-prompt">提示词测试</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">请求参数</label>
                        <div id="requestParams">
                            <!-- 请求参数将在这里动态生成 -->
                        </div>
                        <div id="batchInfo" class="alert alert-info" style="display: none;">
                            <i class="fas fa-info-circle"></i> 
                            <strong>批处理模式：</strong>将处理所有已选择的文档（共 <span id="selectedCount">0</span> 个）
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>请求预览</h5>
                    <div class="response-area" id="requestPreview">
                        请求内容将在这里显示
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-secondary btn-step" onclick="previousStep(3)">
                    <i class="fas fa-arrow-left"></i> 上一步
                </button>
                <button class="btn btn-success btn-step" onclick="submitRequest()" id="submitBtn">
                    <i class="fas fa-paper-plane"></i> 提交请求
                </button>
            </div>
        </div>

        <!-- 步骤4: 分析结果 -->
        <div class="workflow-panel" id="panel4" style="display: none;">
            <h3><i class="fas fa-chart-bar"></i> 分析结果</h3>
            <div class="loading" id="loadingIndicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">正在处理请求...</p>
            </div>
            <div id="resultsContainer">
                <div class="row">
                    <div class="col-md-6">
                        <h5>API响应</h5>
                        <div class="response-area" id="apiResponse">
                            等待响应...
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>分析结果</h5>
                        <div class="analysis-result" id="analysisResult">
                            <div class="mb-2">
                                <span class="status-indicator status-pending"></span>
                                <strong>状态:</strong> <span id="analysisStatus">等待分析</span>
                            </div>
                            <div class="mb-2">
                                <strong>质量分数:</strong> <span id="qualityScore">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>执行时间:</strong> <span id="executionTime">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>置信度:</strong> <span id="confidenceScores">-</span>
                            </div>
                            <div id="detailedAnalysis">
                                <!-- 详细分析结果将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-secondary btn-step" onclick="previousStep(4)">
                    <i class="fas fa-arrow-left"></i> 上一步
                </button>
                <button class="btn btn-primary btn-step" onclick="exportResults()">
                    <i class="fas fa-download"></i> 导出结果
                </button>
                <button class="btn btn-success btn-step" onclick="startNewAnalysis()">
                    <i class="fas fa-plus"></i> 新建分析
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script>
        // 全局变量
        let currentStep = 1;
        let selectedDocument = null;
        let selectedTemplate = null;
        let promptEditor = null;
        let analysisResults = null;
        let selectedFiles = [];
        let fileContents = {};

        // API配置
        const API_BASE_URL = 'http://localhost:8006';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            console.log('Initializing app...');
            
            // 初始化CodeMirror编辑器
            promptEditor = CodeMirror.fromTextArea(document.getElementById('promptEditor'), {
                lineNumbers: true,
                mode: 'javascript',
                theme: 'monokai',
                lineWrapping: true
            });

            // 监听提示词变化
            promptEditor.on('change', function() {
                document.getElementById('promptLength').textContent = promptEditor.getValue().length;
            });

            // 设置文件输入监听
            setupFileInput();
            
            // 加载API配置信息
            loadApiConfig();
            
            // 加载模板列表
            loadTemplates();
            
            console.log('App initialized');
        }

        // 设置文件输入
        function setupFileInput() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', function(e) {
                handleFileSelect(e.target.files);
            });
        }

        // 处理文件选择
        function handleFileSelect(files) {
            console.log('handleFileSelect called with files:', files);
            selectedFiles = [];
            fileContents = {};
            
            const selectedFilesList = document.getElementById('selectedFilesList');
            selectedFilesList.innerHTML = '';
            
            if (files.length === 0) {
                document.getElementById('nextStep1').disabled = true;
                return;
            }
            
            // 处理每个文件
            Array.from(files).forEach((file, index) => {
                const fileId = `file_${index}_${Date.now()}`;
                const fileInfo = {
                    id: fileId,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    file: file
                };
                
                selectedFiles.push(fileInfo);
                
                // 创建文件项
                const fileItem = document.createElement('div');
                fileItem.className = 'document-item';
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <strong>${file.name}</strong>
                            <small class="text-muted d-block">${formatFileSize(file.size)} • ${getFileTypeName(file.type)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFile('${fileId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                fileItem.onclick = (e) => {
                    if (!e.target.closest('button')) {
                        selectDocument(fileInfo);
                    }
                };
                
                selectedFilesList.appendChild(fileItem);
                
                // 读取文件内容
                readFileContent(file, fileId);
            });
            
            // 默认选择第一个文件
            if (selectedFiles.length > 0) {
                console.log('Auto-selecting first file:', selectedFiles[0]);
                selectDocument(selectedFiles[0]);
            }
        }

        // 移除文件
        function removeFile(fileId) {
            selectedFiles = selectedFiles.filter(f => f.id !== fileId);
            delete fileContents[fileId];
            
            // 重新渲染文件列表
            const selectedFilesList = document.getElementById('selectedFilesList');
            selectedFilesList.innerHTML = '';
            
            selectedFiles.forEach(fileInfo => {
                const fileItem = document.createElement('div');
                fileItem.className = 'document-item';
                if (selectedDocument && selectedDocument.id === fileInfo.id) {
                    fileItem.classList.add('selected');
                }
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <strong>${fileInfo.name}</strong>
                            <small class="text-muted d-block">${formatFileSize(fileInfo.size)} • ${getFileTypeName(fileInfo.type)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFile('${fileInfo.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                fileItem.onclick = (e) => {
                    if (!e.target.closest('button')) {
                        selectDocument(fileInfo);
                    }
                };
                selectedFilesList.appendChild(fileItem);
            });
            
            // 如果没有文件了，禁用下一步
            if (selectedFiles.length === 0) {
                selectedDocument = null;
                document.getElementById('documentPreview').textContent = '请选择文件';
                document.getElementById('nextStep1').disabled = true;
            } else if (selectedDocument && !selectedFiles.find(f => f.id === selectedDocument.id)) {
                // 如果当前选中的文件被删除了，选择第一个文件
                selectDocument(selectedFiles[0]);
            }
        }

        // 读取文件内容
        function readFileContent(file, fileId) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                let content = e.target.result;
                
                // 根据文件类型处理内容
                if (file.type === 'application/pdf') {
                    // PDF文件需要特殊处理，这里先显示基本信息
                    content = `PDF文件: ${file.name}\n大小: ${formatFileSize(file.size)}\n\nPDF内容解析需要后端支持`;
                } else if (file.name.match(/\.(doc|docx)$/)) {
                    // Word文档需要特殊处理
                    content = `Word文档: ${file.name}\n大小: ${formatFileSize(file.size)}\n\nWord文档内容解析需要后端支持`;
                } else if (file.name.match(/\.(xls|xlsx|csv)$/)) {
                    // Excel文件需要特殊处理
                    content = `Excel文件: ${file.name}\n大小: ${formatFileSize(file.size)}\n\nExcel文件内容解析需要后端支持`;
                } else {
                    // 文本文件，直接显示内容（限制长度）
                    if (content.length > 5000) {
                        content = content.substring(0, 5000) + '\n\n... [内容过长，只显示前5000字符]';
                    }
                }
                
                fileContents[fileId] = content;
                
                // 如果这是当前选中的文件，更新预览
                if (selectedDocument && selectedDocument.id === fileId) {
                    document.getElementById('documentPreview').textContent = content;
                }
            };
            
            reader.onerror = function() {
                fileContents[fileId] = `无法读取文件: ${file.name}`;
                if (selectedDocument && selectedDocument.id === fileId) {
                    document.getElementById('documentPreview').textContent = fileContents[fileId];
                }
            };
            
            // 根据文件类型选择读取方式
            if (file.type.startsWith('text/') || file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                // 对于二进制文件，读取为DataURL
                reader.readAsDataURL(file);
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取文件类型名称
        function getFileTypeName(mimeType) {
            if (mimeType.startsWith('text/')) return '文本文件';
            if (mimeType === 'application/pdf') return 'PDF文档';
            if (mimeType.includes('word')) return 'Word文档';
            if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'Excel表格';
            if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return 'Word文档';
            if (mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') return 'Excel表格';
            return '未知类型';
        }

        // 选择文档
        function selectDocument(doc) {
            console.log('selectDocument called with:', doc);
            selectedDocument = doc;
            
            // 更新UI
            document.querySelectorAll('.document-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 找到对应的文件项并添加选中状态
            const fileItems = document.querySelectorAll('.document-item');
            fileItems.forEach(item => {
                if (item.textContent.includes(doc.name)) {
                    item.classList.add('selected');
                }
            });
            
            // 显示文档预览
            const content = fileContents[doc.id] || `正在加载 ${doc.name} 的内容...`;
            document.getElementById('documentPreview').textContent = content;
            
            // 启用下一步按钮
            const nextButton = document.getElementById('nextStep1');
            if (nextButton) {
                nextButton.disabled = false;
                console.log('Document selected, next button enabled');
                
                // 添加测试按钮
                const testButton = document.createElement('button');
                testButton.className = 'btn btn-success btn-sm ms-2';
                testButton.textContent = '测试下一步';
                testButton.onclick = () => {
                    console.log('测试按钮点击');
                    alert('当前选中文档: ' + selectedDocument.name);
                };
                nextButton.parentNode.appendChild(testButton);
            } else {
                console.error('Next button not found');
            }
        }

      
        // 加载API配置信息
        async function loadApiConfig() {
            try {
                // 获取配置信息
                const configResponse = await fetch(`${API_BASE_URL}/config`);
                const configData = await configResponse.json();
                
                // 获取健康检查信息
                const healthResponse = await fetch(`${API_BASE_URL}/health`);
                const healthData = await healthResponse.json();
                
                // 更新UI显示
                updateApiConfigDisplay(configData, healthData);
                
            } catch (error) {
                console.error('加载API配置失败:', error);
                updateApiConfigDisplay(null, null, error);
            }
        }
        
        // 更新API配置显示
        function updateApiConfigDisplay(configData, healthData, error = null) {
            const statusIndicator = document.getElementById('apiStatusIndicator');
            const statusText = document.getElementById('apiStatusText');
            const llmProvider = document.getElementById('llmProvider');
            const llmModel = document.getElementById('llmModel');
            const apiMode = document.getElementById('apiMode');
            const baseUrl = document.getElementById('baseUrl');
            const serviceStatus = document.getElementById('serviceStatus');
            const lastUpdate = document.getElementById('lastUpdate');
            
            if (error) {
                // 显示错误状态
                statusIndicator.className = 'status-indicator status-error';
                statusText.textContent = 'API连接失败';
                llmProvider.textContent = '连接错误';
                llmModel.textContent = '-';
                apiMode.textContent = '离线';
                baseUrl.textContent = '-';
                serviceStatus.innerHTML = '<span class="status-indicator status-error"></span><span>连接失败</span>';
            } else if (configData && healthData) {
                // 显示正常状态
                const isHealthy = healthData.status === 'healthy';
                const isConfigured = healthData.config_loaded && healthData.llm_ready;
                const isMock = configData.use_mock_llm;
                
                // 更新状态指示器
                if (isHealthy && isConfigured) {
                    statusIndicator.className = 'status-indicator status-success';
                    statusText.textContent = 'API配置正常';
                } else {
                    statusIndicator.className = 'status-indicator status-pending';
                    statusText.textContent = 'API配置需要检查';
                }
                
                // 更新LLM提供商信息
                llmProvider.textContent = configData.llm?.provider || '未知';
                llmModel.textContent = configData.llm?.model || '未知';
                
                // 更新API模式
                if (isMock) {
                    apiMode.textContent = '模拟模式 (Mock)';
                    apiMode.style.color = '#ffc107';
                } else {
                    apiMode.textContent = '真实API模式';
                    apiMode.style.color = '#28a745';
                }
                
                // 更新Base URL
                baseUrl.textContent = configData.llm?.base_url || '默认配置';
                
                // 更新服务状态
                if (isHealthy) {
                    serviceStatus.innerHTML = '<span class="status-indicator status-success"></span><span>运行正常</span>';
                } else {
                    serviceStatus.innerHTML = '<span class="status-indicator status-error"></span><span>服务异常</span>';
                }
                
                // 更新最后检查时间
                lastUpdate.textContent = new Date().toLocaleString();
            } else {
                // 显示未知状态
                statusIndicator.className = 'status-indicator status-pending';
                statusText.textContent = '配置状态未知';
                llmProvider.textContent = '-';
                llmModel.textContent = '-';
                apiMode.textContent = '-';
                baseUrl.textContent = '-';
                serviceStatus.innerHTML = '<span class="status-indicator status-pending"></span><span>未知</span>';
            }
        }

        // 刷新API配置状态
        async function refreshApiConfig() {
            const refreshBtn = document.getElementById('refreshConfigBtn');
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
            }
            
            await loadApiConfig();
            
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新';
            }
        }

        // 加载模板列表
        async function loadTemplates() {
            try {
                const response = await fetch(`${API_BASE_URL}/templates`);
                const data = await response.json();
                
                const templateList = document.getElementById('templateList');
                templateList.innerHTML = '';

                // 添加默认模板
                const defaultTemplates = [
                    { name: 'unified_analysis', description: '统一分析模板', complexity: '中等' },
                    { name: 'quick_summary', description: '快速摘要模板', complexity: '简单' },
                    { name: 'detailed_analysis', description: '详细分析模板', complexity: '复杂' },
                    { name: 'sentiment_analysis', description: '情感分析模板', complexity: '中等' }
                ];

                defaultTemplates.forEach(template => {
                    const templateCard = document.createElement('div');
                    templateCard.className = 'template-card';
                    templateCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${template.name}</strong>
                                <small class="text-muted d-block">${template.description}</small>
                            </div>
                            <span class="badge bg-secondary">${template.complexity}</span>
                        </div>
                    `;
                    templateCard.onclick = () => selectTemplate(template);
                    templateList.appendChild(templateCard);
                });
            } catch (error) {
                console.error('加载模板列表失败:', error);
            }
        }

        // 选择模板
        async function selectTemplate(template) {
            selectedTemplate = template;
            
            // 更新UI
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // 加载模板内容
            try {
                const response = await fetch(`${API_BASE_URL}/templates/${template.name}`);
                const data = await response.json();
                
                // 设置提示词编辑器内容
                if (data.template) {
                    promptEditor.setValue(data.template);
                } else {
                    // 模拟模板内容
                    const mockTemplate = `请分析以下文档：

文档标题：{title}
文档内容：{content}

请提供以下分析：
1. 文档主题分析
2. 关键信息提取
3. 情感倾向分析
4. 行动建议

请以JSON格式返回结果。`;
                    promptEditor.setValue(mockTemplate);
                }
                
                // 生成变量输入
                generateVariableInputs(data.variables || {});
                
            } catch (error) {
                console.error('加载模板失败:', error);
            }
        }

        // 生成变量输入
        function generateVariableInputs(variables) {
            const variableInputs = document.getElementById('variableInputs');
            variableInputs.innerHTML = '';
            
            const defaultVariables = {
                'title': '文档标题',
                'content': '文档内容',
                'language': '语言',
                'output_format': '输出格式'
            };
            
            Object.entries(defaultVariables).forEach(([key, label]) => {
                const inputGroup = document.createElement('div');
                inputGroup.className = 'mb-2';
                inputGroup.innerHTML = `
                    <label class="form-label small">${label}</label>
                    <input type="text" class="form-control form-control-sm" id="var_${key}" placeholder="输入${label}">
                `;
                variableInputs.appendChild(inputGroup);
            });
        }

        // 步骤导航
        function nextStep(step) {
            console.log('nextStep called with step:', step);
            console.log('selectedDocument:', selectedDocument);
            console.log('currentStep:', currentStep);
            
            if (step === 1 && !selectedDocument) {
                alert('请先选择一个文档');
                return;
            }
            
            if (step === 2 && !promptEditor.getValue().trim()) {
                alert('请输入提示词');
                return;
            }
            
            try {
                // 隐藏当前面板
                const currentPanel = document.getElementById(`panel${step}`);
                const currentStepElement = document.getElementById(`step${step}`);
                
                if (currentPanel) {
                    currentPanel.style.display = 'none';
                }
                if (currentStepElement) {
                    currentStepElement.classList.remove('active');
                    currentStepElement.classList.add('completed');
                }
                
                // 显示下一步面板
                currentStep = step + 1;
                const nextPanel = document.getElementById(`panel${currentStep}`);
                const nextStepElement = document.getElementById(`step${currentStep}`);
                
                if (nextPanel) {
                    nextPanel.style.display = 'block';
                }
                if (nextStepElement) {
                    nextStepElement.classList.add('active');
                }
                
                console.log(`成功切换到步骤 ${currentStep}`);
                
                // 更新请求预览
                if (currentStep === 3) {
                    updateRequestPreview();
                    setupEndpointChangeListener();
                }
            } catch (error) {
                console.error('步骤切换失败:', error);
                alert('步骤切换失败，请检查控制台');
            }
        }

        function previousStep(step) {
            // 隐藏当前面板
            document.getElementById(`panel${step}`).style.display = 'none';
            document.getElementById(`step${step}`).classList.remove('active');
            
            // 显示上一步面板
            currentStep = step - 1;
            document.getElementById(`panel${currentStep}`).style.display = 'block';
            document.getElementById(`step${currentStep}`).classList.remove('completed');
            document.getElementById(`step${currentStep}`).classList.add('active');
        }

        // 设置API端点变化监听器
        function setupEndpointChangeListener() {
            const endpointSelect = document.getElementById('apiEndpoint');
            const batchInfo = document.getElementById('batchInfo');
            const selectedCount = document.getElementById('selectedCount');
            
            // 更新批处理信息显示
            function updateBatchInfo() {
                const endpoint = endpointSelect.value;
                if (endpoint === 'batch') {
                    batchInfo.style.display = 'block';
                    selectedCount.textContent = selectedFiles.length;
                } else {
                    batchInfo.style.display = 'none';
                }
                updateRequestPreview();
            }
            
            // 移除旧的事件监听器（如果存在）
            endpointSelect.removeEventListener('change', updateBatchInfo);
            // 添加新的事件监听器
            endpointSelect.addEventListener('change', updateBatchInfo);
            
            // 初始更新
            updateBatchInfo();
        }

        // 更新请求预览
        function updateRequestPreview() {
            const endpoint = document.getElementById('apiEndpoint').value;
            
            if (endpoint === 'analyze/upload') {
                // 文件上传预览
                document.getElementById('requestPreview').textContent = 
                    `端点: ${endpoint}\n\n文件上传请求:\n- 文件名: ${selectedDocument.name}\n- 文件大小: ${formatFileSize(selectedDocument.size)}\n- 文件类型: ${selectedDocument.type}\n- 模板: ${selectedTemplate ? selectedTemplate.name : 'unified_analysis'}\n- 文档ID: ${selectedDocument.id}`;
            } else if (endpoint === 'batch') {
                // 批处理预览
                const batchPreview = {
                    endpoint: endpoint,
                    template_name: selectedTemplate ? selectedTemplate.name : 'unified_analysis',
                    document_count: selectedFiles.length,
                    documents: selectedFiles.map(file => ({
                        doc_id: file.id,
                        title: file.name,
                        size: formatFileSize(file.size),
                        preview: (fileContents[file.id] || '').substring(0, 100) + ((fileContents[file.id] || '').length > 100 ? '...' : '')
                    }))
                };
                
                document.getElementById('requestPreview').textContent = 
                    `端点: ${endpoint}\n\n批处理请求:\n${JSON.stringify(batchPreview, null, 2)}`;
            } else {
                // 文本分析预览
                const content = fileContents[selectedDocument.id] || '';
                const requestData = {
                    doc_id: selectedDocument.id,
                    title: selectedDocument.name,
                    text: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
                    template_name: selectedTemplate ? selectedTemplate.name : 'unified_analysis'
                };
                
                document.getElementById('requestPreview').textContent = 
                    `端点: ${endpoint}\n\n请求数据:\n${JSON.stringify(requestData, null, 2)}`;
            }
        }

        // 提交请求
        async function submitRequest() {
            const submitBtn = document.getElementById('submitBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            
            // 显示加载状态
            submitBtn.disabled = true;
            loadingIndicator.classList.add('show');
            
            try {
                const endpoint = document.getElementById('apiEndpoint').value;
                let result;
                
                if (endpoint === 'analyze/upload') {
                    // 使用文件上传端点
                    const formData = new FormData();
                    formData.append('file', selectedDocument.file);
                    formData.append('template_name', selectedTemplate ? selectedTemplate.name : 'unified_analysis');
                    formData.append('doc_id', selectedDocument.id);
                    
                    const response = await fetch(`${API_BASE_URL}/analyze/upload`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    result = await response.json();
                } else if (endpoint === 'batch') {
                    // 使用批处理端点
                    const batchData = {
                        documents: [],
                        template_name: selectedTemplate ? selectedTemplate.name : 'unified_analysis'
                    };
                    
                    // 处理所有选中的文件
                    for (const fileInfo of selectedFiles) {
                        const content = fileContents[fileInfo.id] || '';
                        batchData.documents.push({
                            doc_id: fileInfo.id,
                            title: fileInfo.name,
                            text: content
                        });
                    }
                    
                    const response = await fetch(`${API_BASE_URL}/batch`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(batchData)
                    });
                    
                    result = await response.json();
                } else {
                    // 使用文本分析端点
                    const content = fileContents[selectedDocument.id] || '';
                    const requestData = {
                        doc_id: selectedDocument.id,
                        title: selectedDocument.name,
                        text: content,
                        template_name: selectedTemplate ? selectedTemplate.name : 'unified_analysis'
                    };
                    
                    const response = await fetch(`${API_BASE_URL}/${endpoint}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });
                    
                    result = await response.json();
                }
                
                // 显示结果
                displayResults(result);
                
                // 进入结果页面
                nextStep(3);
                
            } catch (error) {
                console.error('请求失败:', error);
                alert('请求失败，请检查API服务是否启动');
            } finally {
                submitBtn.disabled = false;
                loadingIndicator.classList.remove('show');
            }
        }

        // 显示结果
        function displayResults(results) {
            analysisResults = results;
            
            // 显示API响应
            document.getElementById('apiResponse').textContent = JSON.stringify(results, null, 2);
            
            // 显示分析结果
            const endpoint = document.getElementById('apiEndpoint').value;
            const analysisStatus = document.getElementById('analysisStatus');
            const statusIndicator = analysisStatus.previousElementSibling;
            
            if (endpoint === 'batch') {
                // 批处理结果显示
                analysisStatus.textContent = `批处理完成 (${results.results ? results.results.length : 0} 个文档)`;
                statusIndicator.className = 'status-indicator status-success';
                
                // 显示批处理统计
                if (results.results && results.results.length > 0) {
                    const avgQuality = results.results.reduce((sum, r) => sum + (r.quality_score || 0), 0) / results.results.length;
                    const totalTime = results.total_execution_time || results.execution_time || 0;
                    
                    document.getElementById('qualityScore').textContent = avgQuality.toFixed(2) + ' (平均)';
                    document.getElementById('executionTime').textContent = totalTime.toFixed(2) + ' 秒';
                    document.getElementById('confidenceScores').textContent = `处理了 ${results.results.length} 个文档`;
                    
                    // 显示每个文档的详细结果
                    const detailedAnalysis = document.getElementById('detailedAnalysis');
                    detailedAnalysis.innerHTML = '<h6>批处理详细结果:</h6>';
                    
                    results.results.forEach((result, index) => {
                        const docResult = document.createElement('div');
                        docResult.className = 'mb-3 p-3 border rounded';
                        docResult.innerHTML = `
                            <strong>文档 ${index + 1}: ${result.title || '未知文档'}</strong><br>
                            质量分数: ${(result.quality_score || 0).toFixed(2)}<br>
                            执行时间: ${(result.execution_time || 0).toFixed(2)} 秒<br>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="toggleDocDetail(${index})">
                                查看详细分析
                            </button>
                            <div id="docDetail${index}" style="display: none; margin-top: 10px;">
                                <pre>${JSON.stringify(result.analysis || result, null, 2)}</pre>
                            </div>
                        `;
                        detailedAnalysis.appendChild(docResult);
                    });
                } else {
                    document.getElementById('qualityScore').textContent = '-';
                    document.getElementById('executionTime').textContent = '-';
                    document.getElementById('confidenceScores').textContent = '无结果';
                }
            } else {
                // 单文档结果显示
                analysisStatus.textContent = '分析完成';
                statusIndicator.className = 'status-indicator status-success';
                
                // 提取并显示关键指标
                if (results.quality_score) {
                    document.getElementById('qualityScore').textContent = results.quality_score.toFixed(2);
                }
                if (results.execution_time) {
                    document.getElementById('executionTime').textContent = results.execution_time.toFixed(2) + ' 秒';
                }
                if (results.confidence_scores) {
                    document.getElementById('confidenceScores').textContent = 
                        Object.entries(results.confidence_scores)
                            .map(([key, value]) => `${key}: ${(value * 100).toFixed(1)}%`)
                            .join(', ');
                }
                
                // 显示详细分析
                const detailedAnalysis = document.getElementById('detailedAnalysis');
                detailedAnalysis.innerHTML = '<h6>详细分析:</h6>';
                
                if (results.analysis) {
                    Object.entries(results.analysis).forEach(([key, value]) => {
                        const analysisItem = document.createElement('div');
                        analysisItem.className = 'mb-2';
                        analysisItem.innerHTML = `<strong>${key}:</strong> ${JSON.stringify(value)}`;
                        detailedAnalysis.appendChild(analysisItem);
                    });
                }
            }
        }

        // 切换文档详细结果显示
        function toggleDocDetail(index) {
            const detailDiv = document.getElementById(`docDetail${index}`);
            const button = event.target;
            
            if (detailDiv.style.display === 'none') {
                detailDiv.style.display = 'block';
                button.textContent = '隐藏详细分析';
            } else {
                detailDiv.style.display = 'none';
                button.textContent = '查看详细分析';
            }
        }

        // 导出结果
        function exportResults() {
            if (!analysisResults) return;
            
            const endpoint = document.getElementById('apiEndpoint').value;
            let exportData;
            
            if (endpoint === 'batch') {
                // 批处理导出
                exportData = {
                    analysis_type: 'batch',
                    document_count: selectedFiles.length,
                    template: selectedTemplate,
                    results: analysisResults,
                    export_time: new Date().toISOString()
                };
                
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `batch_analysis_${selectedFiles.length}_docs_${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
            } else {
                // 单文档导出
                exportData = {
                    analysis_type: 'single',
                    document: selectedDocument,
                    template: selectedTemplate,
                    prompt: promptEditor.getValue(),
                    results: analysisResults,
                    export_time: new Date().toISOString()
                };
                
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `analysis_${selectedDocument.id}_${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        // 开始新分析
        function startNewAnalysis() {
            // 重置状态
            currentStep = 1;
            selectedDocument = null;
            selectedTemplate = null;
            analysisResults = null;
            
            // 重置UI
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active', 'completed');
            });
            document.getElementById('step1').classList.add('active');
            
            document.querySelectorAll('.workflow-panel').forEach(panel => {
                panel.style.display = 'none';
            });
            document.getElementById('panel1').style.display = 'block';
            
            // 重置选择状态
            document.querySelectorAll('.document-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 重置按钮状态
            document.getElementById('nextStep1').disabled = true;
            
            // 清空编辑器
            promptEditor.setValue('');
            
            // 清空预览
            document.getElementById('documentPreview').textContent = '请选择一个文档进行预览';
        }
    </script>
</body>
</html>