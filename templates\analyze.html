{% extends "base.html" %}

{% block title %}分析工具 - 模块化政策叙事分析系统{% endblock %}

{% block content %}
<div class="container my-5">
    <h1 class="mb-4">模块化分析工具</h1>
    <p class="lead mb-4">使用此工具分析政策文档并生成结构化结果。</p>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    文档输入
                </div>
                <div class="card-body">
                    <form id="analysisForm" method="POST" action="/api/modular_analysis">
                        <div class="mb-3">
                            <label for="documentTitle" class="form-label">文档标题</label>
                            <input type="text" class="form-control" id="documentTitle" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="documentText" class="form-label">文档内容</label>
                            <textarea class="form-control" id="documentText" name="text" rows="10" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="analysisMode" class="form-label">分析模式</label>
                            <select class="form-select" id="analysisMode" name="mode">
                                <option value="unified">统一分析（全部任务）</option>
                                <option value="stepwise">逐步分析（依次执行任务）</option>
                                <option value="selective">选择性分析（自定义任务）</option>
                            </select>
                        </div>
                        <div class="mb-3" id="selectiveTasksContainer" style="display:none;">
                            <label class="form-label">选择任务</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tasks" value="topic" id="taskTopic">
                                <label class="form-check-label" for="taskTopic">主题分析</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tasks" value="sentiment" id="taskSentiment">
                                <label class="form-check-label" for="taskSentiment">情感分析</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tasks" value="actors" id="taskActors">
                                <label class="form-check-label" for="taskActors">参与者分析</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tasks" value="causal" id="taskCausal">
                                <label class="form-check-label" for="taskCausal">因果机制</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="language" class="form-label">语言</label>
                            <select class="form-select" id="language" name="language">
                                <option value="en">英语</option>
                                <option value="zh">中文</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">开始分析</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    分析结果
                </div>
                <div class="card-body">
                    <div id="loadingIndicator" style="display:none;" class="text-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在分析，请稍候...</p>
                    </div>
                    <div id="resultContainer">
                        <div class="alert alert-info">
                            填写表单并提交以查看分析结果。分析可能需要几秒钟时间。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const analysisForm = document.getElementById('analysisForm');
    const analysisMode = document.getElementById('analysisMode');
    const selectiveTasksContainer = document.getElementById('selectiveTasksContainer');
    const resultContainer = document.getElementById('resultContainer');
    const loadingIndicator = document.getElementById('loadingIndicator');

    // 根据分析模式显示/隐藏任务选择
    analysisMode.addEventListener('change', function() {
        if (this.value === 'selective') {
            selectiveTasksContainer.style.display = 'block';
        } else {
            selectiveTasksContainer.style.display = 'none';
        }
    });

    // 处理表单提交
    analysisForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 显示加载指示器
        resultContainer.innerHTML = '';
        loadingIndicator.style.display = 'block';
        
        // 准备请求数据
        const formData = new FormData(analysisForm);
        const requestData = {
            doc_id: Date.now().toString(),
            title: formData.get('title'),
            text: formData.get('text'),
            mode: formData.get('mode'),
            language: formData.get('language')
        };
        
        // 如果是选择性分析，添加选定的任务
        if (formData.get('mode') === 'selective') {
            const selectedTasks = [];
            document.querySelectorAll('input[name="tasks"]:checked').forEach(checkbox => {
                selectedTasks.push(checkbox.value);
            });
            
            if (selectedTasks.length === 0) {
                alert('请选择至少一个分析任务');
                loadingIndicator.style.display = 'none';
                return;
            }
            
            requestData.tasks = selectedTasks;
        }
        
        // 发送API请求
        fetch('/api/modular_analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // 隐藏加载指示器
            loadingIndicator.style.display = 'none';
            
            // 显示结果
            displayResults(data);
        })
        .catch(error => {
            // 隐藏加载指示器
            loadingIndicator.style.display = 'none';
            
            // 显示错误信息
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h5>分析失败</h5>
                    <p>${error.message || '请检查您的输入并重试'}</p>
                </div>
            `;
        });
    });
    
    function displayResults(data) {
        // 创建美观的结果显示
        let resultHtml = `
            <div class="mb-4">
                <h4>分析结果</h4>
                <div class="d-flex align-items-center mb-2">
                    <span class="me-2">质量分数:</span>
                    <div class="progress flex-grow-1" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${data.quality_score * 100}%;" 
                            aria-valuenow="${data.quality_score * 100}" aria-valuemin="0" aria-valuemax="100">
                            ${(data.quality_score * 100).toFixed(0)}%
                        </div>
                    </div>
                </div>
                <p class="mb-2">执行时间: ${data.execution_time.toFixed(2)}秒</p>
            </div>
        `;
        
        if (data.analysis && Object.keys(data.analysis).length > 0) {
            resultHtml += `<div class="accordion" id="resultAccordion">`;
            
            // 遍历分析结果的各个部分
            Object.entries(data.analysis).forEach(([key, value], index) => {
                const headingId = `heading${index}`;
                const collapseId = `collapse${index}`;
                const taskTitle = getTaskTitle(key);
                
                resultHtml += `
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="${headingId}">
                            <button class="accordion-button ${index > 0 ? 'collapsed' : ''}" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#${collapseId}" 
                                    aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                                ${taskTitle}
                            </button>
                        </h2>
                        <div id="${collapseId}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                             aria-labelledby="${headingId}" data-bs-parent="#resultAccordion">
                            <div class="accordion-body">
                                <pre class="result-json">${JSON.stringify(value, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultHtml += `</div>`;
        } else {
            resultHtml += `
                <div class="alert alert-warning">
                    <p>未返回分析结果。请尝试使用不同的分析选项。</p>
                </div>
            `;
        }
        
        resultContainer.innerHTML = resultHtml;
    }
    
    function getTaskTitle(task) {
        const taskTitles = {
            'topic_analysis': '主题分析',
            'sentiment_analysis': '情感分析',
            'actor_analysis': '参与者分析',
            'causal_mechanisms': '因果机制',
            'summary': '摘要',
            'recommendations': '建议',
            'unified': '统一分析'
        };
        
        return taskTitles[task] || task;
    }
});
</script>
<style>
.result-json {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 0.875rem;
    max-height: 400px;
    overflow-y: auto;
}
</style>
{% endblock %}
