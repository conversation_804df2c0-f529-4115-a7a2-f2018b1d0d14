{% extends "base.html" %}

{% block title %}LLM响应可视化仪表板{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        position: relative;
        height: 350px;
        margin-bottom: 30px;
    }
    .stat-card {
        transition: all 0.3s;
    }
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .quality-critical { color: #dc3545; }
    .quality-low { color: #fd7e14; }
    .quality-moderate { color: #ffc107; }
    .quality-good { color: #198754; }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="mb-3">LLM响应可视化仪表板</h1>
            <p class="lead">实时监控和分析LLM响应的质量、性能和趋势。</p>
        </div>
    </div>

    <!-- Date range filter -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-3">
                            <label for="start-date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start-date" name="start_date">
                        </div>
                        <div class="col-md-3">
                            <label for="end-date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end-date" name="end_date">
                        </div>
                        <div class="col-md-3">
                            <label for="language-filter" class="form-label">语言</label>
                            <select class="form-select" id="language-filter" name="language">
                                <option value="">全部</option>
                                <option value="en">英文</option>
                                <option value="zh">中文</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">应用过滤器</button>
                            <button type="button" id="refresh-btn" class="btn btn-secondary ms-2">刷新数据</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="h4">摘要统计</h2>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">总响应数</h5>
                    <h2 id="total-responses">{{ stats.total_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">平均质量分数</h5>
                    <h2 id="avg-quality" class="{{ 'quality-good' if stats.avg_quality >= 0.7 else 'quality-moderate' if stats.avg_quality >= 0.5 else 'quality-low' if stats.avg_quality >= 0.3 else 'quality-critical' }}">
                        {{ "%.2f"|format(stats.avg_quality) }}
                    </h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">平均响应时间</h5>
                    <h2 id="avg-duration">{{ "%.2f"|format(stats.avg_duration) }}s</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">语言分布</h5>
                    <div id="language-distribution">
                        <span class="badge bg-primary me-1">英文: {{ stats.language_distribution.en }}</span>
                        <span class="badge bg-success">中文: {{ stats.language_distribution.zh }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Quality Distribution Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">质量分数分布</h5>
                    <div class="chart-container">
                        <canvas id="quality-distribution-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Task Quality Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">任务类型质量对比</h5>
                    <div class="chart-container">
                        <canvas id="task-quality-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Response Time Distribution -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">响应时间分布</h5>
                    <div class="chart-container">
                        <canvas id="time-distribution-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Language Quality Comparison -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">语言质量对比</h5>
                    <div class="chart-container">
                        <canvas id="language-comparison-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quality Trend Chart -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">质量分数趋势</h5>
                    <div class="chart-container">
                        <canvas id="quality-trend-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Responses Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">最近响应</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>文档ID</th>
                                    <th>模式</th>
                                    <th>任务</th>
                                    <th>语言</th>
                                    <th>质量分数</th>
                                    <th>响应时间</th>
                                    <th>时间戳</th>
                                </tr>
                            </thead>
                            <tbody id="responses-table-body">
                                {% for resp in recent_responses %}
                                <tr>
                                    <td>{{ resp.document_id }}</td>
                                    <td>{{ resp.mode }}</td>
                                    <td>{{ resp.tasks|join(", ") }}</td>
                                    <td>{{ resp.language }}</td>
                                    <td class="{{ 'quality-good' if resp.quality_score >= 0.7 else 'quality-moderate' if resp.quality_score >= 0.5 else 'quality-low' if resp.quality_score >= 0.3 else 'quality-critical' }}">
                                        {{ "%.2f"|format(resp.quality_score) }}
                                    </td>
                                    <td>{{ "%.2f"|format(resp.duration) }}s</td>
                                    <td>{{ resp.timestamp }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 从模板获取数据
    const qualityDistribution = {{ quality_distribution|tojson }};
    const taskQuality = {
        labels: {{ task_quality.labels|tojson }},
        data: {{ task_quality.data|tojson }}
    };
    const timeDistribution = {{ time_distribution|tojson }};
    const languageComparison = {{ language_comparison|tojson }};
    const trendData = {
        dates: {{ trend_data.dates|tojson }},
        scores: {{ trend_data.scores|tojson }},
        rollingAvg: {{ trend_data.rolling_avg|tojson }}
    };
    
    // Quality Distribution Chart
    const qualityCtx = document.getElementById('quality-distribution-chart').getContext('2d');
    const qualityChart = new Chart(qualityCtx, {
        type: 'bar',
        data: {
            labels: ['严重问题 (<0.3)', '低质量 (0.3-0.5)', '中等质量 (0.5-0.7)', '良好质量 (>0.7)'],
            datasets: [{
                label: '响应数量',
                data: qualityDistribution,
                backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#198754'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '响应数量: ' + context.raw;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '响应数量'
                    }
                }
            }
        }
    });

    // Task Quality Chart
    const taskQualityCtx = document.getElementById('task-quality-chart').getContext('2d');
    const taskQualityChart = new Chart(taskQualityCtx, {
        type: 'bar',
        data: {
            labels: taskQuality.labels,
            datasets: [{
                label: '平均质量分数',
                data: taskQuality.data,
                backgroundColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    title: {
                        display: true,
                        text: '平均质量分数'
                    }
                }
            }
        }
    });

    // Response Time Distribution Chart
    const timeCtx = document.getElementById('time-distribution-chart').getContext('2d');
    const timeChart = new Chart(timeCtx, {
        type: 'bar',
        data: {
            labels: Array.from({length: timeDistribution.length}, (_, i) => i + 1),
            datasets: [{
                label: '响应时间',
                data: timeDistribution,
                backgroundColor: '#36b9cc',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '响应时间（秒）'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '频率'
                    }
                }
            }
        }
    });

    // Language Quality Comparison Chart
    const langCtx = document.getElementById('language-comparison-chart').getContext('2d');
    // 计算平均值
    const enScores = languageComparison[0];
    const zhScores = languageComparison[1];
    const enAvg = enScores.length > 0 ? enScores.reduce((a, b) => a + b, 0) / enScores.length : 0;
    const zhAvg = zhScores.length > 0 ? zhScores.reduce((a, b) => a + b, 0) / zhScores.length : 0;
    
    const langChart = new Chart(langCtx, {
        type: 'bar',
        data: {
            labels: ['英文', '中文'],
            datasets: [{
                label: '平均质量分数',
                data: [enAvg, zhAvg],
                backgroundColor: ['rgba(78, 115, 223, 0.5)', 'rgba(54, 185, 204, 0.5)'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    title: {
                        display: true,
                        text: '质量分数'
                    }
                }
            }
        }
    });

    // Quality Trend Chart
    const trendCtx = document.getElementById('quality-trend-chart').getContext('2d');
    const trendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: trendData.dates,
            datasets: [{
                label: '质量分数',
                data: trendData.scores,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                fill: true,
                pointBackgroundColor: '#4e73df',
                pointRadius: 3
            },
            {
                label: '移动平均',
                data: trendData.rollingAvg,
                borderColor: '#1cc88a',
                backgroundColor: 'transparent',
                fill: false,
                borderDash: [5, 5],
                pointRadius: 0
            },
            {
                label: '质量阈值',
                data: Array(trendData.dates.length).fill(0.5),
                borderColor: '#f6c23e',
                backgroundColor: 'transparent',
                fill: false,
                borderDash: [3, 3],
                pointRadius: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    title: {
                        display: true,
                        text: '质量分数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                }
            }
        }
    });

    // Initialize filter form
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            // Implement filter functionality here
            // For now, we'll just reload the page with query parameters
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            const language = document.getElementById('language-filter').value;
            window.location.href = `?start_date=${startDate}&end_date=${endDate}&language=${language}`;
        });
    }

    // Handle refresh button
    document.getElementById('refresh-btn').addEventListener('click', function() {
        window.location.reload();
    });
});
</script>
{% endblock %}
