#!/usr/bin/env python3
"""
文档分析工作流启动器

启动API服务器和前端界面，提供完整的文档分析工作流。
"""

import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    try:
        import fastapi
        import uvicorn
        print("OK - FastAPI 依赖已安装")
    except ImportError:
        print("ERROR - 缺少FastAPI依赖，请运行: pip install fastapi uvicorn")
        return False
    
    return True

def start_api_server():
    """启动API服务器"""
    print("正在启动API服务器...")
    os.system("python web_api.py")

def start_frontend():
    """启动前端服务器"""
    print("正在启动前端界面...")
    
    # 简单的HTTP服务器来服务前端文件
    import http.server
    import socketserver
    
    class FrontendHandler(http.server.SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory="frontend", **kwargs)
    
    with socketserver.TCPServer(("", 8088), FrontendHandler) as httpd:
        print("前端服务器已启动: http://localhost:8088")
        httpd.serve_forever()

def open_browser():
    """打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open("http://localhost:8088")

def main():
    print("=== 文档分析工作流启动器 ===")
    print()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查文件
    if not Path("web_api.py").exists():
        print("ERROR - 找不到 web_api.py 文件")
        sys.exit(1)
    
    if not Path("frontend/index.html").exists():
        print("ERROR - 找不到前端文件")
        sys.exit(1)
    
    print("OK - 所有文件检查通过")
    print()
    
    # 启动API服务器（在后台线程）
    api_thread = threading.Thread(target=start_api_server)
    api_thread.daemon = True
    api_thread.start()
    
    # 启动前端服务器（在后台线程）
    frontend_thread = threading.Thread(target=start_frontend)
    frontend_thread.daemon = True
    frontend_thread.start()
    
    # 打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print()
    print("=== 工作流已启动 ===")
    print("API服务器: http://localhost:8006")
    print("前端界面: http://localhost:8088")
    print("API文档: http://localhost:8006/docs")
    print()
    print("按 Ctrl+C 停止服务器")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        sys.exit(0)

if __name__ == "__main__":
    main()