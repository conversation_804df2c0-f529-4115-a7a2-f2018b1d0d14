"""
Web API for the Document Analysis System.

Provides RESTful API endpoints for document analysis and prompt testing.
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from dotenv import load_dotenv
import logging
import time
import traceback

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from contextlib import asynccontextmanager
import uvicorn
from src.core.api_monitor import APIMonitor

from src.core.config import Config
from src.core.analyzer import DocumentAnalyzer, AnalysisResult
from src.core.modular_analyzer import ModularDocumentAnalyzer, ModularAnalysisResult
from src.core.modular_prompts import AnalysisMode, TaskType
from src.core.prompts import PromptTemplates
from src.utils.document_reader import DocumentReader, Document
from src.testing.prompt_tester import PromptTester
from src.visualization.llm_stats import LLMResponseAnalyzer
from src.core.response_collector import ResponseCollector

# Load environment variables
load_dotenv()

# Debug: Print environment variables (remove in production)
print("=== Environment Variables ===")
print(f"ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
print(f"LLM_PROVIDER: {os.getenv('LLM_PROVIDER')}")
print(f"USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM')}")
print("==========================")

# Global variables
config: Config = None
analyzer: DocumentAnalyzer = None
modular_analyzer: ModularDocumentAnalyzer = None
prompt_tester: PromptTester = None
document_reader: DocumentReader = None
prompt_templates: PromptTemplates = None
llm_visualizer: LLMResponseAnalyzer = None
response_collector: ResponseCollector = None

# 初始化模板系统
templates = Jinja2Templates(directory="templates")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    print("Starting up Document Analysis System API...")
    success = initialize_app()
    if not success:
        print("Failed to initialize application")
    
    yield
    
    # Shutdown
    print("Shutting down Document Analysis System API...")

# Initialize FastAPI app
app = FastAPI(
    title="Document Analysis System API",
    description="RESTful API for document analysis and prompt testing",
    version="1.0.0",
    lifespan=lifespan
)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# API monitor for performance tracking
api_monitor = APIMonitor()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class DocumentRequest(BaseModel):
    doc_id: str
    title: Optional[str] = None
    text: str
    known_actors: Optional[List[str]] = None
    template_name: str = "unified_analysis"

class DocumentResponse(BaseModel):
    doc_id: str
    title: Optional[str] = None
    analysis: Dict[str, Any]
    quality_score: float
    execution_time: float
    confidence_scores: Dict[str, float]
    warnings: List[str]
    metadata: Dict[str, Any]

class PromptTestRequest(BaseModel):
    template_name: str
    document_id: str
    document_text: str
    document_title: Optional[str] = None
    variables: Optional[Dict[str, str]] = None

class BatchAnalysisRequest(BaseModel):
    documents: List[DocumentRequest]
    template_name: str = "unified_analysis"

class ConfigRequest(BaseModel):
    llm_provider: str = "openai"
    llm_model: str = "gpt-4"
    temperature: float = 0.0
    max_tokens: int = 4000
    use_mock_llm: bool = False
    min_confidence_score: float = 0.7
    max_document_length: int = 10000

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    config_loaded: bool
    llm_ready: bool

def initialize_app():
    """Initialize the application with configuration."""
    global config, analyzer, modular_analyzer, prompt_tester, document_reader, prompt_templates, llm_visualizer, response_collector
    
    try:
        # Load configuration (this will load environment variables in __post_init__)
        config = Config()
        
        # Check if we should use mock LLM due to missing API keys
        if not config.use_mock_llm and not config.llm.api_key:
            print("Warning: LLM API key not set. Using mock LLM client.")
            os.environ["USE_MOCK_LLM"] = "true"
            config.use_mock_llm = True
        
        # Initialize components
        analyzer = DocumentAnalyzer(config)
        modular_analyzer = ModularDocumentAnalyzer(config)
        prompt_tester = PromptTester(config)
        document_reader = DocumentReader()
        prompt_templates = PromptTemplates()
        
        # 初始化可视化和响应收集器
        results_dir = os.path.join("results", "llm_responses")
        output_dir = os.path.join("results", "visualizations")
        
        # 创建必要的目录
        os.makedirs(results_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        
        llm_visualizer = LLMResponseAnalyzer(
            results_dir=results_dir,
            output_dir=output_dir
        )
        response_collector = ResponseCollector(storage_dir=results_dir)
        
        print("Application initialized successfully")
        return True
    except Exception as e:
        print(f"Error initializing application: {str(e)}")
        traceback.print_exc()
        return False

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Document Analysis System API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy" if config else "unhealthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        config_loaded=config is not None,
        llm_ready=analyzer is not None
    )

@app.get("/debug-env")
async def debug_env():
    """Debug endpoint to check environment variables."""
    from dotenv import load_dotenv
    load_dotenv()
    import os
    
    return {
        "environment_variables": {
            "ZHIPUAI_API_KEY": "SET" if os.getenv('ZHIPUAI_API_KEY') else "NOT SET",
            "LLM_PROVIDER": os.getenv('LLM_PROVIDER'),
            "USE_MOCK_LLM": os.getenv('USE_MOCK_LLM'),
            "OPENAI_API_KEY": "SET" if os.getenv('OPENAI_API_KEY') else "NOT SET",
            "ANTHROPIC_API_KEY": "SET" if os.getenv('ANTHROPIC_API_KEY') else "NOT SET"
        }
    }

@app.get("/test-config")
async def test_config():
    """Test configuration endpoint."""
    from dotenv import load_dotenv
    load_dotenv()
    from src.core.config import Config
    
    # Create a fresh config
    fresh_config = Config()
    
    return {
        "config": {
            "provider": fresh_config.llm.provider,
            "model": fresh_config.llm.model,
            "api_key_set": bool(fresh_config.llm.api_key),
            "use_mock": fresh_config.use_mock_llm
        },
        "environment": {
            "LLM_PROVIDER": os.getenv('LLM_PROVIDER'),
            "LLM_MODEL": os.getenv('LLM_MODEL'),
            "USE_MOCK_LLM": os.getenv('USE_MOCK_LLM'),
            "ZHIPUAI_API_KEY": "SET" if os.getenv('ZHIPUAI_API_KEY') else "NOT SET"
        }
    }

@app.get("/config", response_model=Dict[str, Any])
async def get_config():
    """Get current configuration."""
    global config
    
    print("=== Config Endpoint Called ===")
    if config is None:
        print("Config is None")
        return {"error": "Configuration not initialized"}
    
    print(f"Config provider: {config.llm.provider}")
    print(f"Config model: {config.llm.model}")
    print(f"Config use_mock: {config.use_mock_llm}")
    print(f"Config api_key: {'SET' if config.llm.api_key else 'NOT SET'}")
    
    result = config.to_dict()
    print(f"Result provider: {result['llm']['provider']}")
    print(f"Result model: {result['llm']['model']}")
    print(f"Result use_mock: {result['use_mock_llm']}")
    print("===========================")
    
    return result

@app.post("/config", response_model=Dict[str, str])
async def update_config(config_request: ConfigRequest):
    """Update configuration."""
    global config, analyzer, prompt_tester
    
    try:
        # Update configuration
        config.llm.provider = config_request.llm_provider
        config.llm.model = config_request.llm_model
        config.llm.temperature = config_request.temperature
        config.llm.max_tokens = config_request.max_tokens
        config.use_mock_llm = config_request.use_mock_llm
        config.analysis.min_confidence_score = config_request.min_confidence_score
        config.analysis.max_document_length = config_request.max_document_length
        
        # Reinitialize components
        analyzer = DocumentAnalyzer(config)
        prompt_tester = PromptTester(config)
        
        return {"message": "Configuration updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update configuration: {str(e)}")

@app.post("/analyze", response_model=DocumentResponse)
async def analyze_document(request: DocumentRequest):
    """Analyze a single document."""
    if not analyzer:
        raise HTTPException(status_code=500, detail="Analyzer not initialized")
    
    try:
        # Create document object
        document = Document(
            doc_id=request.doc_id,
            title=request.title,
            text=request.text,
            metadata={"known_actors": request.known_actors or []}
        )
        
        # Analyze document
        result = analyzer.analyze_document(document, request.template_name)
        
        return DocumentResponse(
            doc_id=result.doc_id,
            title=result.title,
            analysis=result.analysis or {},
            quality_score=result.quality_score,
            execution_time=result.execution_time,
            confidence_scores=result.confidence_scores,
            warnings=result.warnings,
            metadata=result.metadata
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analyze/modular", response_model=Dict[str, Any])
@app.post("/api/modular_analysis", response_model=Dict[str, Any])  # 添加兼容路由
async def analyze_document_modular(request: Dict[str, Any]):
    """Analyze document using modular approach.
    
    Request body:
    {
        "doc_id": "string",
        "title": "string",
        "text": "string",
        "mode": "unified|stepwise|selective",  // optional, default: stepwise
        "tasks": ["actors_relationships", "portrayals", ...],  // optional, default: all
        "language": "en|zh"  // optional, default: en
    }
    """
    if not modular_analyzer:
        raise HTTPException(status_code=500, detail="Modular analyzer not initialized")
    
    try:
        # Start performance tracking
        start_time = time.time()
        api_monitor.start_request("analyze_document_modular")
        
        # Get parameters from request
        doc_id = request.get("doc_id", f"doc_{int(time.time())}")
        title = request.get("title", None)
        text = request.get("text")
        mode_str = request.get("mode", "stepwise").lower()
        tasks = request.get("tasks", None)
        language = request.get("language", "en").lower()
        
        if not text:
            raise HTTPException(status_code=400, detail="Text is required")
        
        # Validate and convert mode
        try:
            mode = AnalysisMode(mode_str)
        except ValueError:
            raise HTTPException(status_code=400, 
                               detail=f"Invalid mode: {mode_str}. Must be one of: {[m.value for m in AnalysisMode]}")
        
        # Create document
        document = Document(doc_id=doc_id, title=title, text=text)
        
        # Analyze document
        result: ModularAnalysisResult = modular_analyzer.analyze_modular(
            document=document,
            mode=mode,
            tasks=tasks,
            language=language
        )
        
        # End performance tracking
        duration = time.time() - start_time
        api_monitor.record_request("analyze_document_modular", 200, duration)
        
        # Format response
        response = {
            "doc_id": doc_id,
            "title": title,
            "mode": mode.value,
            "language": language,
            "tasks": result.tasks,
            "analysis": result.analysis,
            "quality_score": result.quality_score,
            "execution_time": result.execution_time,
            "timestamp": datetime.now().isoformat(),
            "warnings": result.warnings,
            "task_results": result.task_results
        }
        
        # 保存响应数据用于可视化
        if response_collector:
            response_collector.save_response(response)
        
        return response
    
    except Exception as e:
        # Record error
        api_monitor.record_request("analyze_document_modular", 500, time.time() - start_time)
        
        # Log error
        logging.error(f"Error in modular analysis: {str(e)}")
        traceback.print_exc()
        
        raise HTTPException(status_code=500, detail=f"Modular analysis failed: {str(e)}")

@app.get("/analyze/modes")
async def get_analysis_modes():
    """Get available analysis modes."""
    return {
        "modes": [
            {
                "name": "unified",
                "description": "All tasks analyzed in a single prompt",
                "best_for": "Quick comprehensive analysis"
            },
            {
                "name": "stepwise",
                "description": "Tasks analyzed sequentially with dependencies",
                "best_for": "Detailed, accurate analysis with task dependencies"
            },
            {
                "name": "selective",
                "description": "Only selected tasks are analyzed",
                "best_for": "Focused analysis on specific aspects"
            }
        ],
        "tasks": [
            {
                "name": "actors_relationships",
                "description": "Extract actors and their relationships",
                "dependencies": []
            },
            {
                "name": "portrayals",
                "description": "Detect hero, victim, and devil portrayals",
                "dependencies": ["actors_relationships"]
            },
            {
                "name": "issue_scope",
                "description": "Identify issue expansion and containment strategies",
                "dependencies": []
            },
            {
                "name": "causal_mechanisms",
                "description": "Detect intentional and inadvertent causal mechanisms",
                "dependencies": []
            }
        ]
    }

@app.post("/analyze/upload", response_model=DocumentResponse)
async def analyze_uploaded_file(
    file: UploadFile = File(...),
    template_name: str = "unified_analysis",
    doc_id: Optional[str] = None
):
    """Analyze an uploaded file."""
    if not analyzer or not document_reader:
        raise HTTPException(status_code=500, detail="System not initialized")
    
    try:
        # Save uploaded file
        upload_dir = Path(config.output_dir) / "uploads"
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Read document
        document = document_reader.read_file(file_path)
        
        # Use provided doc_id or generate one
        if doc_id:
            document.doc_id = doc_id
        
        # Analyze document
        result = analyzer.analyze_document(document, template_name)
        
        # Clean up uploaded file
        file_path.unlink()
        
        return DocumentResponse(
            doc_id=result.doc_id,
            title=result.title,
            analysis=result.analysis or {},
            quality_score=result.quality_score,
            execution_time=result.execution_time,
            confidence_scores=result.confidence_scores,
            warnings=result.warnings,
            metadata=result.metadata
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File analysis failed: {str(e)}")

@app.post("/batch", response_model=List[DocumentResponse])
async def analyze_batch(request: BatchAnalysisRequest):
    """Analyze multiple documents in batch."""
    if not analyzer:
        raise HTTPException(status_code=500, detail="Analyzer not initialized")
    
    try:
        documents = []
        for doc_request in request.documents:
            document = Document(
                doc_id=doc_request.doc_id,
                title=doc_request.title,
                text=doc_request.text,
                metadata={"known_actors": doc_request.known_actors or []}
            )
            documents.append(document)
        
        # Analyze documents
        results = analyzer.analyze_documents_batch(documents, request.template_name)
        
        # Convert to response format
        responses = []
        for result in results:
            response = DocumentResponse(
                doc_id=result.doc_id,
                title=result.title,
                analysis=result.analysis or {},
                quality_score=result.quality_score,
                execution_time=result.execution_time,
                confidence_scores=result.confidence_scores,
                warnings=result.warnings,
                metadata=result.metadata
            )
            responses.append(response)
        
        return responses
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")

@app.post("/test-prompt")
async def test_prompt(request: PromptTestRequest):
    """Test a prompt template."""
    if not prompt_tester:
        raise HTTPException(status_code=500, detail="Prompt tester not initialized")
    
    try:
        # Create document
        document = Document(
            doc_id=request.document_id,
            title=request.document_title,
            text=request.document_text
        )
        
        # Test prompt
        result = prompt_tester.test_template(
            request.template_name,
            document,
            request.variables
        )
        
        return {
            "test_id": result.test_id,
            "template_name": result.template_name,
            "document_id": result.document_id,
            "parse_success": result.parse_success,
            "execution_time": result.execution_time,
            "quality_score": result.quality_score,
            "error_message": result.error_message,
            "parsed_result": result.parsed_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prompt test failed: {str(e)}")

@app.get("/templates")
async def get_templates():
    """Get available prompt templates."""
    if not prompt_templates:
        raise HTTPException(status_code=500, detail="Prompt templates not initialized")
    
    templates_info = {}
    for name, template in prompt_templates.get_all_templates().items():
        templates_info[name] = {
            "description": template.description,
            "complexity": template.complexity,
            "estimated_tokens": template.estimated_tokens,
            "best_for": template.best_for,
            "variables": template.variables
        }
    
    return {"templates": templates_info}

@app.get("/templates/{template_name}")
async def get_template(template_name: str):
    """Get specific prompt template."""
    if not prompt_templates:
        raise HTTPException(status_code=500, detail="Prompt templates not initialized")
    
    template = prompt_templates.get_template(template_name)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    return {
        "name": template.name,
        "description": template.description,
        "template": template.template,
        "complexity": template.complexity,
        "estimated_tokens": template.estimated_tokens,
        "best_for": template.best_for,
        "variables": template.variables
    }

@app.get("/metrics")
async def get_metrics():
    """Get system metrics."""
    if not analyzer:
        raise HTTPException(status_code=500, detail="Analyzer not initialized")
    
    summary = analyzer.get_analysis_summary()
    return summary

@app.post("/test-llm")
async def test_llm():
    """Test LLM connection."""
    if not analyzer:
        raise HTTPException(status_code=500, detail="Analyzer not initialized")
    
    try:
        # Create test document
        from src.utils.document_reader import create_sample_document
        document = create_sample_document()
        
        # Test analysis
        result = analyzer.analyze_document(document)
        
        return {
            "status": "success",
            "message": "LLM connection test successful",
            "execution_time": result.execution_time,
            "quality_score": result.quality_score,
            "warnings": len(result.warnings)
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"LLM connection test failed: {str(e)}"
        }

# 网页路由 - 首页
@app.get("/home", response_class=HTMLResponse)
async def home(request: Request):
    """网站首页"""
    return templates.TemplateResponse(
        "base.html", 
        {"request": request}
    )

# 网页路由 - 分析工具页面
@app.get("/analyze", response_class=HTMLResponse)
async def analyze_tool(request: Request):
    """分析工具网页界面"""
    return templates.TemplateResponse(
        "analyze.html",
        {"request": request}
    )

# 网页路由 - 可视化仪表板
@app.get("/visualizations", response_class=HTMLResponse)
async def visualizations_dashboard(request: Request):
    """LLM响应可视化仪表板"""
    if not llm_visualizer:
        raise HTTPException(status_code=500, detail="Visualizer not initialized")
    
    try:
        # 解析查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        language = request.query_params.get('language')
        
        # 如果没有指定日期范围，默认使用过去30天
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 加载响应数据
        record_count = llm_visualizer.load_response_data(pattern="*.json")
        
        if record_count == 0:
            return templates.TemplateResponse(
                "visualizations.html",
                {
                    "request": request,
                    "stats": {"total_count": 0, "avg_quality": 0, "avg_duration": 0, "language_distribution": {"en": 0, "zh": 0}},
                    "quality_distribution": [0, 0, 0, 0],
                    "task_quality": {"labels": [], "data": []},
                    "time_distribution": [],
                    "language_comparison": [[], []],
                    "trend_data": {"dates": [], "scores": [], "rolling_avg": []},
                    "recent_responses": []
                }
            )
        
        # 生成统计数据
        stats = llm_visualizer.generate_summary_statistics()
        
        # 生成可视化数据
        quality_distribution = llm_visualizer.get_quality_tier_counts()
        task_quality = llm_visualizer.get_task_quality_data()
        time_distribution = llm_visualizer.df['duration'].tolist()  # 简化处理
        
        # 语言质量对比
        en_scores = llm_visualizer.df[llm_visualizer.df['language'] == 'en']['quality_score'].tolist()
        zh_scores = llm_visualizer.df[llm_visualizer.df['language'] == 'zh']['quality_score'].tolist()
        language_comparison = [en_scores, zh_scores]
        
        # 质量趋势数据
        trend_data = llm_visualizer.get_quality_trend_data()
        
        # 最近响应数据
        recent_responses = llm_visualizer.df.sort_values('timestamp', ascending=False).head(10).to_dict('records')
        
        # 渲染模板
        return templates.TemplateResponse(
            "visualizations.html",
            {
                "request": request,
                "stats": stats,
                "quality_distribution": quality_distribution,
                "task_quality": task_quality,
                "time_distribution": time_distribution,
                "language_comparison": language_comparison,
                "trend_data": trend_data,
                "recent_responses": recent_responses
            }
        )
    except Exception as e:
        logging.error(f"Error generating visualizations: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error generating visualizations: {str(e)}")

# API路由 - 获取可视化数据
@app.get("/api/visualizations/data")
async def get_visualization_data(request: Request):
    """获取LLM响应可视化数据的API端点"""
    if not llm_visualizer:
        raise HTTPException(status_code=500, detail="Visualizer not initialized")
    
    try:
        # 解析查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        language = request.query_params.get('language')
        
        # 重新加载数据（以获取最新的响应）
        llm_visualizer.load_response_data(pattern="*.json")
        
        # TODO: 根据过滤条件筛选数据
        
        # 生成统计数据
        stats = llm_visualizer.generate_summary_statistics()
        
        # 生成可视化数据
        quality_distribution = llm_visualizer.get_quality_tier_counts()
        task_quality = llm_visualizer.get_task_quality_data()
        time_distribution = llm_visualizer.df['duration'].tolist()
        
        # 语言质量对比
        en_scores = llm_visualizer.df[llm_visualizer.df['language'] == 'en']['quality_score'].tolist()
        zh_scores = llm_visualizer.df[llm_visualizer.df['language'] == 'zh']['quality_score'].tolist()
        language_comparison = [en_scores, zh_scores]
        
        # 质量趋势数据
        trend_data = llm_visualizer.get_quality_trend_data()
        
        # 最近响应数据
        recent_responses = llm_visualizer.df.sort_values('timestamp', ascending=False).head(10).to_dict('records')
        
        # 返回所有数据
        return {
            "stats": stats,
            "quality_distribution": quality_distribution,
            "task_quality": task_quality,
            "time_distribution": time_distribution,
            "language_comparison": language_comparison,
            "trend_data": trend_data,
            "recent_responses": recent_responses
        }
    except Exception as e:
        logging.error(f"Error getting visualization data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting visualization data: {str(e)}")

# 手动生成可视化图表的API端点
@app.post("/api/visualizations/generate")
async def generate_visualizations():
    """手动生成所有可视化图表"""
    if not llm_visualizer:
        raise HTTPException(status_code=500, detail="Visualizer not initialized")
    
    try:
        # 加载响应数据
        record_count = llm_visualizer.load_response_data()
        
        if record_count == 0:
            return {"message": "No response data found"}
        
        # 生成所有可视化
        viz_paths = llm_visualizer.generate_all_visualizations()
        
        return {
            "message": f"Generated {len(viz_paths)} visualizations",
            "visualizations": viz_paths
        }
    except Exception as e:
        logging.error(f"Error generating visualizations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating visualizations: {str(e)}")

if __name__ == "__main__":
    # Initialize app
    initialize_app()
    
    # Run server
    uvicorn.run(
        "web_api:app",
        host="0.0.0.0",
        port=8006,
        reload=True,
        log_level="info"
    )