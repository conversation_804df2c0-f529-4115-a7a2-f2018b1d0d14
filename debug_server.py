#!/usr/bin/env python3
"""
Simple frontend server for testing
"""

import http.server
import socketserver
import webbrowser
import threading
import time

class FrontendHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory="frontend", **kwargs)

def start_server():
    with socketserver.TCPServer(("", 8089), FrontendHandler) as httpd:
        print("Frontend server started at http://localhost:8089")
        httpd.serve_forever()

def open_browser():
    time.sleep(2)
    webbrowser.open("http://localhost:8089/debug.html")

if __name__ == "__main__":
    # Start server in background thread
    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    # Open browser
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("Debug server started!")
    print("Access debug page at: http://localhost:8089/debug.html")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping server...")