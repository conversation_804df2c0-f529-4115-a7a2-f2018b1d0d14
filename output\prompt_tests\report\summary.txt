Prompt Testing Report\n====================\n\nGenerated: 2025-08-26 16:13:26\n\nOverall Metrics:\n- Total Tests: 12\n- Successful Tests: 12\n- Failed Tests: 0\n- Success Rate: 100.00%\n- Average Execution Time: 0.10s\n- Average Quality Score: 0.27\n\nTemplate Performance:\n--------------------------------------------------\n\nunified_analysis:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.25\n\nactor_extraction:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.10\n\nrelationship_analysis:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.60\n\nportrayal_analysis:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.25\n\nissue_scope_analysis:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.10\n\ncausal_mechanism_analysis:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.60\n\nquality_assessment:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.25\n\nvalidation_check:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.10\n\nopenai_optimized:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.60\n\nanthropic_optimized:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.25\n\nzhipu_optimized:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.10\n\nquick_summary:\n  - Tests: 1\n  - Success Rate: 100.00%\n  - Avg Execution Time: 0.10s\n  - Avg Quality Score: 0.00\n