"""
Debug script to test LLM responses and fix JSON parsing issues.
"""

import os
import json
from dotenv import load_dotenv
from src.core.config import Config
from src.core.llm_client import create_llm_client
from src.utils.document_reader import create_sample_document

def test_llm_response():
    """Test raw LLM response to see what's being returned."""
    
    # Load environment
    load_dotenv()
    
    # Create config
    config = Config()
    print(f"Using LLM: {config.llm.provider} ({config.llm.model})")
    print(f"Mock mode: {config.use_mock_llm}")
    
    # Create LLM client
    llm_client = create_llm_client(config.llm, config.use_mock_llm)
    
    # Create a simple test prompt
    test_prompt = """
You are an information extraction model. Extract actors from the following text and return ONLY a valid JSON object.

Text: "The Environmental Protection Agency (EPA) announced new regulations targeting industrial polluters."

Return ONLY this JSON structure:
{
  "actors": [
    {
      "name": "string",
      "type": "organization"
    }
  ]
}
"""
    
    print("\n" + "="*60)
    print("SENDING TEST PROMPT")
    print("="*60)
    print(test_prompt)
    
    # Get response
    print("\n" + "="*60)
    print("RAW LLM RESPONSE")
    print("="*60)
    
    try:
        response = llm_client.generate_response(test_prompt)
        print(f"Response type: {type(response)}")
        print(f"Response length: {len(response)}")
        print("\nFull response:")
        print(response)
        
        # Try to parse as JSON
        print("\n" + "="*60)
        print("PARSING ATTEMPT")
        print("="*60)
        
        try:
            # Try to find JSON in the response
            # Sometimes LLMs add extra text before/after JSON
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                print(f"Extracted JSON substring: {json_str[:100]}...")
                
                parsed = json.loads(json_str)
                print("✓ Successfully parsed JSON!")
                print(json.dumps(parsed, indent=2))
            else:
                print("✗ No JSON object found in response")
                
        except json.JSONDecodeError as e:
            print(f"✗ JSON parse error: {e}")
            
            # Try to clean the response
            print("\nAttempting to clean response...")
            
            # Remove markdown code blocks if present
            if "```json" in response:
                response = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                response = response.split("```")[1].split("```")[0].strip()
            
            # Try parsing again
            try:
                parsed = json.loads(response)
                print("✓ Successfully parsed after cleaning!")
                print(json.dumps(parsed, indent=2))
            except Exception as e2:
                print(f"✗ Still failed after cleaning: {e2}")
    
    except Exception as e:
        print(f"Error getting LLM response: {e}")
        import traceback
        traceback.print_exc()

def test_with_system_message():
    """Test with explicit system message for JSON output."""
    
    load_dotenv()
    config = Config()
    
    print("\n" + "="*60)
    print("TESTING WITH SYSTEM MESSAGE")
    print("="*60)
    
    # Modify the prompt to be more explicit
    test_prompt = """Respond ONLY with valid JSON. No explanations, no markdown, just JSON.

Extract actors from: "The EPA announced new regulations."

Output format:
{"actors": [{"name": "EPA", "type": "organization"}]}"""
    
    llm_client = create_llm_client(config.llm, config.use_mock_llm)
    
    try:
        response = llm_client.generate_response(test_prompt)
        print(f"Response: {response}")
        
        # Clean response
        response = response.strip()
        if response.startswith("```"):
            # Remove markdown code blocks
            lines = response.split('\n')
            response = '\n'.join(lines[1:-1])
        
        parsed = json.loads(response)
        print("✓ Parsed successfully!")
        print(json.dumps(parsed, indent=2))
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("="*60)
    print("LLM DEBUG SCRIPT")
    print("="*60)
    
    # Test 1: Basic response
    test_llm_response()
    
    # Test 2: With system message
    test_with_system_message()
    
    print("\n" + "="*60)
    print("DEBUG COMPLETE")
    print("="*60)
