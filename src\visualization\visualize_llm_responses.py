#!/usr/bin/env python3
"""
Command Line Interface for LLM response visualization.

This script provides a CLI for generating visualizations of LLM response data.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.visualization.llm_stats import LLMResponseAnalyzer

def main():
    """Main entry point for the visualization CLI."""
    parser = argparse.ArgumentParser(description='Visualize LLM response data')
    parser.add_argument('--results-dir', type=str, default='results/llm_responses',
                        help='Directory containing LLM response data files')
    parser.add_argument('--output-dir', type=str, default='results/visualizations',
                        help='Directory for saving visualization outputs')
    parser.add_argument('--pattern', type=str, default='*.json',
                        help='File pattern to match for LLM response data')
    parser.add_argument('--all', action='store_true',
                        help='Generate all available visualizations')
    parser.add_argument('--quality-dist', action='store_true',
                        help='Generate quality distribution visualization')
    parser.add_argument('--task-quality', action='store_true',
                        help='Generate quality by task visualization')
    parser.add_argument('--response-time', action='store_true',
                        help='Generate response time distribution visualization')
    parser.add_argument('--language-comparison', action='store_true',
                        help='Generate language comparison visualization')
    parser.add_argument('--quality-trend', action='store_true',
                        help='Generate quality trend over time visualization')
    parser.add_argument('--stats', action='store_true',
                        help='Generate summary statistics')
    parser.add_argument('--save-data', action='store_true',
                        help='Save processed data to JSON file')
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Create analyzer instance
    analyzer = LLMResponseAnalyzer(
        results_dir=args.results_dir,
        output_dir=args.output_dir
    )
    
    # Load response data
    record_count = analyzer.load_response_data(pattern=args.pattern)
    logger.info(f"Loaded {record_count} records for analysis")
    
    if record_count == 0:
        logger.error(f"No records found in {args.results_dir} matching pattern {args.pattern}")
        sys.exit(1)
    
    # Generate requested visualizations
    results = {}
    
    if args.all or args.quality_dist:
        path = analyzer.plot_quality_distribution()
        if path:
            results['quality_distribution'] = path
            
    if args.all or args.task_quality:
        path = analyzer.plot_quality_by_task()
        if path:
            results['quality_by_task'] = path
            
    if args.all or args.response_time:
        path = analyzer.plot_response_time_distribution()
        if path:
            results['response_time'] = path
            
    if args.all or args.language_comparison:
        path = analyzer.plot_language_comparison()
        if path:
            results['language_comparison'] = path
            
    if args.all or args.quality_trend:
        path = analyzer.plot_quality_trend_over_time()
        if path:
            results['quality_trend'] = path
    
    # Generate and display statistics
    if args.all or args.stats:
        stats = analyzer.generate_summary_statistics()
        if stats:
            logger.info("Summary Statistics:")
            for key, value in stats.items():
                logger.info(f"  {key}: {value}")
                
    # Save processed data
    if args.save_data:
        data_path = analyzer.save_response_data()
        logger.info(f"Saved processed response data to: {data_path}")
    
    # Print results summary
    if results:
        logger.info("Generated visualizations:")
        for viz_type, path in results.items():
            logger.info(f"  {viz_type}: {path}")
    else:
        logger.warning("No visualizations were generated. Try using --all flag.")
        
    return 0

if __name__ == '__main__':
    sys.exit(main())
