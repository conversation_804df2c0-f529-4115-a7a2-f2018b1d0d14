# 文档分析工作流使用指南

## 快速开始

### 1. 启动工作流

**Windows用户:**
```bash
start.bat
```

**其他系统:**
```bash
python start_workflow.py
```

### 2. 访问界面

- **前端工作台**: http://localhost:8088
- **API服务器**: http://localhost:8006
- **API文档**: http://localhost:8006/docs

## 工作流程

### 步骤1: 选择文档
- 从文档列表中选择要分析的文档
- 查看文档预览
- 点击"下一步"继续

### 步骤2: 编辑提示词
- 选择分析模板
- 在提示词编辑器中修改提示词
- 设置变量参数
- 点击"下一步"继续

### 步骤3: 提交请求
- 选择API端点
- 查看请求预览
- 点击"提交请求"发送到API

### 步骤4: 查看结果
- 查看API响应
- 分析结果和质量分数
- 导出结果或开始新的分析

## 功能特性

### 1. 文档管理
- 支持多种文档格式（DOCX, PDF, TXT, XLSX）
- 文档预览功能
- 批量文档处理

### 2. 提示词编辑
- 语法高亮编辑器
- 模板选择和自定义
- 变量参数设置
- 实时字符计数

### 3. API集成
- 与FastAPI后端无缝集成
- 支持多种分析端点
- 实时请求预览
- 错误处理和状态反馈

### 4. 结果分析
- 质量分数评估
- 执行时间统计
- 置信度分析
- 详细结果展示
- 结果导出功能

## 配置说明

### 环境变量
在`.env`文件中设置：
```
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
LLM_PROVIDER=openai
LLM_MODEL=gpt-4
USE_MOCK_LLM=false
```

### API端点
- `POST /analyze` - 单文档分析
- `POST /analyze/upload` - 文件上传分析
- `POST /batch` - 批量分析
- `POST /test-prompt` - 提示词测试
- `GET /templates` - 获取模板列表
- `GET /health` - 健康检查

## 模板系统

### 内置模板
- `unified_analysis` - 统一分析模板
- `quick_summary` - 快速摘要模板
- `detailed_analysis` - 详细分析模板
- `sentiment_analysis` - 情感分析模板

### 自定义模板
可以通过API添加自定义模板，支持：
- 模板描述
- 复杂度等级
- 变量定义
- 输出格式

## 错误处理

### 常见问题
1. **API连接失败**
   - 检查API服务器是否启动
   - 确认端口号正确

2. **文档加载失败**
   - 检查文档格式是否支持
   - 确认文档路径正确

3. **分析结果错误**
   - 检查API密钥配置
   - 确认提示词格式正确

### 调试信息
- 查看浏览器控制台
- 检查API服务器日志
- 使用API文档测试接口

## 扩展功能

### 1. 添加新的文档类型
在`src/utils/document_reader.py`中添加新的文档处理器

### 2. 自定义分析模板
在`src/core/prompts.py`中添加新的模板定义

### 3. 集成其他LLM
在`src/core/config.py`中添加新的LLM提供商配置

## 性能优化

### 1. 批量处理
- 使用批量分析端点
- 合理设置并发数量

### 2. 缓存策略
- 启用结果缓存
- 配置缓存过期时间

### 3. 资源管理
- 监控内存使用
- 设置文档大小限制

## 安全考虑

### 1. API密钥
- 使用环境变量存储
- 不要提交到版本控制

### 2. 文件上传
- 验证文件类型
- 限制文件大小
- 扫描恶意内容

### 3. 访问控制
- 实现用户认证
- 配置访问权限

## 技术栈

- **前端**: HTML5, CSS3, JavaScript, Bootstrap
- **后端**: Python, FastAPI, Uvicorn
- **编辑器**: CodeMirror
- **文档**: Swagger UI
- **样式**: Font Awesome