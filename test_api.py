"""
Test script for the modular API endpoints.
"""

import requests
import json

# API base URL
BASE_URL = "http://localhost:8006"

def test_get_modes():
    """Test the /analyze/modes endpoint."""
    print("\n" + "="*60)
    print("TESTING: GET /analyze/modes")
    print("="*60)
    
    response = requests.get(f"{BASE_URL}/analyze/modes")
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Success!")
        print("\nAvailable Modes:")
        for mode in data.get("modes", []):
            print(f"  - {mode['name']}: {mode['description']}")
        
        print("\nAvailable Tasks:")
        for task in data.get("tasks", []):
            print(f"  - {task['name']}: {task['description']}")
            if task.get("dependencies"):
                print(f"    Dependencies: {task['dependencies']}")
    else:
        print(f"✗ Failed with status {response.status_code}")
        print(response.text)

def test_modular_analysis():
    """Test the /analyze/modular endpoint."""
    print("\n" + "="*60)
    print("TESTING: POST /analyze/modular")
    print("="*60)
    
    # Test document
    payload = {
        "doc_id": "api_test_001",
        "title": "Climate Policy Test",
        "text": """
        The Environmental Protection Agency (EPA) has announced new climate regulations.
        Environmental activists praise the EPA for taking bold action against polluters.
        Industry groups argue the regulations will harm economic growth.
        Local communities affected by pollution support the new measures.
        """,
        "mode": "stepwise",
        "tasks": ["actors_relationships", "portrayals"],
        "language": "en"
    }
    
    print(f"Sending request with mode: {payload['mode']}")
    print(f"Tasks: {payload['tasks']}")
    
    response = requests.post(
        f"{BASE_URL}/analyze/modular",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Success!")
        print(f"\nDocument ID: {data['doc_id']}")
        print(f"Mode: {data['mode']}")
        print(f"Quality Score: {data['quality_score']:.2f}")
        print(f"Execution Time: {data['execution_time']:.2f}s")
        
        print("\nTask Results:")
        for task_name, task_result in data.get("task_results", {}).items():
            print(f"\n  {task_name}:")
            if "error" in task_result:
                print(f"    ✗ Error: {task_result['error']}")
            else:
                if task_name == "actors_relationships":
                    actors = task_result.get("actors", [])
                    print(f"    ✓ Found {len(actors)} actors")
                    for actor in actors[:3]:  # Show first 3
                        print(f"      - {actor.get('name')} ({actor.get('stakeholder_category')})")
                elif task_name == "portrayals":
                    portrayals = task_result.get("portrayals", {}).get("items", [])
                    print(f"    ✓ Found {len(portrayals)} portrayals")
        
        if data.get("warnings"):
            print(f"\nWarnings: {data['warnings']}")
    else:
        print(f"✗ Failed with status {response.status_code}")
        print(response.text)

def test_unified_mode():
    """Test unified analysis mode."""
    print("\n" + "="*60)
    print("TESTING: Unified Mode")
    print("="*60)
    
    payload = {
        "doc_id": "unified_test",
        "title": "Unified Analysis Test",
        "text": "The EPA announced new regulations targeting industrial polluters.",
        "mode": "unified",
        "language": "en"
    }
    
    response = requests.post(
        f"{BASE_URL}/analyze/modular",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Unified analysis successful!")
        print(f"Quality Score: {data['quality_score']:.2f}")
        print(f"Execution Time: {data['execution_time']:.2f}s")
    else:
        print(f"✗ Failed: {response.text[:200]}")

def test_selective_mode():
    """Test selective analysis mode."""
    print("\n" + "="*60)
    print("TESTING: Selective Mode")
    print("="*60)
    
    payload = {
        "doc_id": "selective_test",
        "title": "Selective Analysis Test",
        "text": "The EPA faces criticism from industry groups over new regulations.",
        "mode": "selective",
        "tasks": ["causal_mechanisms"],
        "language": "en"
    }
    
    response = requests.post(
        f"{BASE_URL}/analyze/modular",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Selective analysis successful!")
        print(f"Tasks executed: {list(data.get('task_results', {}).keys())}")
        print(f"Quality Score: {data['quality_score']:.2f}")
    else:
        print(f"✗ Failed: {response.text[:200]}")

def main():
    """Run all API tests."""
    print("="*60)
    print("MODULAR API ENDPOINT TESTS")
    print("="*60)
    
    try:
        # Test 1: Get available modes and tasks
        test_get_modes()
        
        # Test 2: Modular analysis with stepwise mode
        test_modular_analysis()
        
        # Test 3: Unified mode
        test_unified_mode()
        
        # Test 4: Selective mode
        test_selective_mode()
        
        print("\n" + "="*60)
        print("ALL TESTS COMPLETE")
        print("="*60)
        
    except requests.exceptions.ConnectionError:
        print("\n✗ Could not connect to API server")
        print("Make sure the server is running: python web_api.py")
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
