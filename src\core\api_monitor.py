"""
API performance monitoring for document analysis system.
"""
import time
import logging
import threading
import json
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import deque, defaultdict

class APIMonitor:
    """Monitors API performance and provides insights."""
    
    def __init__(self, log_dir: str = "logs/api_stats", window_minutes: int = 15):
        """
        Initialize the API monitor.
        
        Args:
            log_dir: Directory to store performance logs
            window_minutes: Rolling window size in minutes for metrics
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True, parents=True)
        
        self.logger = logging.getLogger(__name__)
        self.window_minutes = window_minutes
        self.window_size = window_minutes * 60  # in seconds
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Performance metrics storage
        self.requests = deque()  # [(timestamp, duration, endpoint, status)]
        self.endpoint_metrics = defaultdict(list)  # endpoint -> [(timestamp, duration, status)]
        self.last_logged = time.time()
        self.log_interval = 300  # Log stats every 5 minutes
        
        # Start background logging thread
        self._start_background_logging()
    
    def _start_background_logging(self):
        """Start background thread for periodic logging."""
        thread = threading.Thread(target=self._periodic_logging, daemon=True)
        thread.start()
    
    def _periodic_logging(self):
        """Periodically log and save metrics."""
        while True:
            time.sleep(60)  # Check every minute
            now = time.time()
            if now - self.last_logged >= self.log_interval:
                with self._lock:
                    self._save_metrics()
                    self.last_logged = now
    
    def record_request(self, endpoint: str, duration: float, status_code: int):
        """
        Record an API request.
        
        Args:
            endpoint: API endpoint path
            duration: Request duration in seconds
            status_code: HTTP status code
        """
        timestamp = time.time()
        
        with self._lock:
            # Add to overall requests
            self.requests.append((timestamp, duration, endpoint, status_code))
            
            # Add to endpoint-specific metrics
            self.endpoint_metrics[endpoint].append((timestamp, duration, status_code))
            
            # Clean up old metrics outside the window
            self._cleanup_old_metrics(timestamp)
    
    def _cleanup_old_metrics(self, current_time: float):
        """Remove metrics outside the rolling window."""
        cutoff = current_time - self.window_size
        
        # Clean overall requests
        while self.requests and self.requests[0][0] < cutoff:
            self.requests.popleft()
        
        # Clean endpoint-specific metrics
        for endpoint in list(self.endpoint_metrics.keys()):
            metrics = self.endpoint_metrics[endpoint]
            while metrics and metrics[0][0] < cutoff:
                metrics.pop(0)
            
            # Remove empty endpoint entries
            if not metrics:
                del self.endpoint_metrics[endpoint]
    
    def get_current_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics.
        
        Returns:
            Dictionary with performance statistics
        """
        with self._lock:
            now = time.time()
            self._cleanup_old_metrics(now)
            
            # Calculate overall stats
            total_requests = len(self.requests)
            if not total_requests:
                return {"status": "No data available in current window"}
            
            # Calculate success rate
            success_count = sum(1 for _, _, _, status in self.requests if 200 <= status < 300)
            success_rate = success_count / total_requests if total_requests else 0
            
            # Calculate response times
            response_times = [duration for _, duration, _, _ in self.requests]
            
            # Endpoint-specific stats
            endpoint_stats = {}
            for endpoint, metrics in self.endpoint_metrics.items():
                if not metrics:
                    continue
                
                ep_times = [duration for _, duration, _ in metrics]
                ep_success = sum(1 for _, _, status in metrics if 200 <= status < 300)
                
                endpoint_stats[endpoint] = {
                    "requests": len(metrics),
                    "success_rate": ep_success / len(metrics) if metrics else 0,
                    "avg_response_time": statistics.mean(ep_times) if ep_times else 0,
                    "p95_response_time": statistics.quantiles(ep_times, n=20)[19] if len(ep_times) >= 20 else max(ep_times, default=0),
                    "max_response_time": max(ep_times) if ep_times else 0
                }
            
            return {
                "timestamp": now,
                "window_minutes": self.window_minutes,
                "total_requests": total_requests,
                "requests_per_minute": total_requests / self.window_minutes,
                "success_rate": success_rate,
                "avg_response_time": statistics.mean(response_times),
                "p95_response_time": statistics.quantiles(response_times, n=20)[19] if len(response_times) >= 20 else max(response_times, default=0),
                "max_response_time": max(response_times),
                "endpoints": endpoint_stats
            }
    
    def _save_metrics(self):
        """Save current metrics to log file."""
        stats = self.get_current_stats()
        if "status" in stats and stats["status"] == "No data available in current window":
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = self.log_dir / f"api_stats_{timestamp}.json"
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, default=str)
            self.logger.info(f"Saved API metrics to {log_file}")
        except Exception as e:
            self.logger.error(f"Failed to save API metrics: {e}")
    
    def get_alerts(self) -> List[str]:
        """
        Get alerts for potential performance issues.
        
        Returns:
            List of alert messages
        """
        alerts = []
        stats = self.get_current_stats()
        
        if "status" in stats:
            return alerts
        
        # Check for high average response time (over 5 seconds)
        if stats["avg_response_time"] > 5:
            alerts.append(f"High average response time: {stats['avg_response_time']:.2f}s")
        
        # Check for low success rate (below 95%)
        if stats["success_rate"] < 0.95:
            alerts.append(f"Low success rate: {stats['success_rate']*100:.1f}%")
        
        # Check for high p95 response time (over 10 seconds)
        if stats["p95_response_time"] > 10:
            alerts.append(f"High p95 response time: {stats['p95_response_time']:.2f}s")
        
        # Check endpoint-specific issues
        for endpoint, ep_stats in stats["endpoints"].items():
            if ep_stats["avg_response_time"] > 8:
                alerts.append(f"Slow endpoint {endpoint}: {ep_stats['avg_response_time']:.2f}s average")
            if ep_stats["success_rate"] < 0.9:
                alerts.append(f"Unreliable endpoint {endpoint}: {ep_stats['success_rate']*100:.1f}% success rate")
        
        return alerts
