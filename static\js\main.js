/**
 * 主要JavaScript文件
 * 为LLM响应可视化仪表板提供交互功能
 */

// 通用函数：格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 通用函数：根据质量分数获取对应的CSS类名
function getQualityClass(score) {
    if (score >= 0.7) return 'quality-good';
    if (score >= 0.5) return 'quality-moderate';
    if (score >= 0.3) return 'quality-low';
    return 'quality-critical';
}

// 通用函数：根据质量分数获取对应的描述
function getQualityDescription(score) {
    if (score >= 0.7) return '良好质量';
    if (score >= 0.5) return '中等质量';
    if (score >= 0.3) return '低质量';
    return '严重问题';
}

// 通用函数：更新统计卡片
function updateStatCards(stats) {
    if (!stats) return;
    
    // 更新总响应数
    const totalResponses = document.getElementById('total-responses');
    if (totalResponses) {
        totalResponses.textContent = stats.total_count;
    }
    
    // 更新平均质量分数
    const avgQuality = document.getElementById('avg-quality');
    if (avgQuality) {
        avgQuality.textContent = stats.avg_quality.toFixed(2);
        avgQuality.className = getQualityClass(stats.avg_quality);
    }
    
    // 更新平均响应时间
    const avgDuration = document.getElementById('avg-duration');
    if (avgDuration) {
        avgDuration.textContent = stats.avg_duration.toFixed(2) + 's';
    }
    
    // 更新语言分布
    const langDist = document.getElementById('language-distribution');
    if (langDist) {
        langDist.innerHTML = `
            <span class="badge bg-primary me-1">英文: ${stats.language_distribution.en || 0}</span>
            <span class="badge bg-success">中文: ${stats.language_distribution.zh || 0}</span>
        `;
    }
}

// 自动加载最近的过滤日期范围
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期范围（过去30天）
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    
    if (startDateInput && endDateInput) {
        // 检查URL中是否有日期参数
        const params = new URLSearchParams(window.location.search);
        const startDate = params.get('start_date');
        const endDate = params.get('end_date');
        const language = params.get('language');
        
        // 设置日期输入框的值
        startDateInput.value = startDate || thirtyDaysAgo.toISOString().split('T')[0];
        endDateInput.value = endDate || today.toISOString().split('T')[0];
        
        // 设置语言过滤器
        const langFilter = document.getElementById('language-filter');
        if (langFilter && language) {
            langFilter.value = language;
        }
    }
    
    // 如果有刷新按钮，添加点击事件
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // 使用当前的过滤器设置重新加载页面
            const form = document.getElementById('filter-form');
            if (form) {
                form.submit();
            } else {
                window.location.reload();
            }
        });
    }
});

// 生成/更新实时响应表格
function updateResponsesTable(responses) {
    const tableBody = document.getElementById('responses-table-body');
    if (!tableBody || !responses || !responses.length) return;
    
    // 清空表格
    tableBody.innerHTML = '';
    
    // 添加行
    responses.forEach(resp => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${resp.document_id}</td>
            <td>${resp.mode}</td>
            <td>${Array.isArray(resp.tasks) ? resp.tasks.join(', ') : resp.tasks}</td>
            <td>${resp.language}</td>
            <td class="${getQualityClass(resp.quality_score)}">${resp.quality_score.toFixed(2)}</td>
            <td>${resp.duration.toFixed(2)}s</td>
            <td>${formatDate(resp.timestamp)}</td>
        `;
        tableBody.appendChild(tr);
    });
}

// 添加定时刷新功能（每60秒自动刷新一次数据）
let autoRefreshEnabled = true;
let refreshInterval;

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    
    const refreshToggle = document.getElementById('refresh-toggle');
    if (refreshToggle) {
        refreshToggle.textContent = autoRefreshEnabled ? '关闭自动刷新' : '开启自动刷新';
    }
    
    if (autoRefreshEnabled) {
        // 启用自动刷新
        refreshInterval = setInterval(() => {
            fetchLatestData();
        }, 60000); // 每60秒刷新
    } else {
        // 停用自动刷新
        clearInterval(refreshInterval);
    }
}

// 加载最新数据（可以通过AJAX调用实现）
function fetchLatestData() {
    const params = new URLSearchParams(window.location.search);
    
    // 构建API请求URL
    let url = '/api/visualizations/data';
    if (params.toString()) {
        url += '?' + params.toString();
    }
    
    // 发送请求获取最新数据
    fetch(url)
        .then(response => response.json())
        .then(data => {
            // 更新图表和表格
            updateCharts(data);
            updateStatCards(data.stats);
            updateResponsesTable(data.recent_responses);
        })
        .catch(error => {
            console.error('获取数据失败:', error);
        });
}
