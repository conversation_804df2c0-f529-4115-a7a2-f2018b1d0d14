#!/usr/bin/env python3
"""
LLM Response Visualization Example.

This script demonstrates how to integrate the ResponseCollector and LLMResponseAnalyzer
to collect, store, analyze, and visualize LLM response data.
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add parent directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.core.response_collector import ResponseCollector
from src.visualization.llm_stats import LLMResponseAnalyzer

def setup_logging(log_level: str = 'INFO'):
    """Configure logging for the script."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
        
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def create_mock_responses(count: int = 10) -> List[Dict[str, Any]]:
    """
    Create mock LLM responses for testing.
    
    Args:
        count: Number of mock responses to generate
        
    Returns:
        List of mock response data dictionaries
    """
    mock_responses = []
    
    # Create a mix of English and Chinese responses with varying quality
    tasks = [
        'actor_extraction', 
        'relationship_analysis', 
        'issue_scope', 
        'causal_mechanisms', 
        'portrayals'
    ]
    
    modes = ['unified', 'stepwise', 'selective']
    languages = ['en', 'zh']
    
    import random
    
    for i in range(count):
        # Mix of quality scores
        if i < count * 0.1:  # 10% critical
            quality_score = random.uniform(0.1, 0.29)
        elif i < count * 0.3:  # 20% low
            quality_score = random.uniform(0.3, 0.49)
        elif i < count * 0.7:  # 40% moderate
            quality_score = random.uniform(0.5, 0.69)
        else:  # 30% good
            quality_score = random.uniform(0.7, 0.95)
            
        # Create random document ID
        doc_id = f"doc_{random.randint(1000, 9999)}"
        
        # Select random mode and language
        mode = random.choice(modes)
        language = random.choice(languages)
        
        # Select 1-3 random tasks
        selected_tasks = random.sample(tasks, random.randint(1, 3))
        
        # Generate random duration (2-10 seconds)
        duration = random.uniform(2, 10)
        
        # Create mock task results
        task_results = {}
        for task in selected_tasks:
            task_results[task] = {
                'success': True,
                'data': {'mock_result': f'Result for {task}'},
                'quality': quality_score * random.uniform(0.8, 1.2)  # Vary per task
            }
            
        # Create the mock response
        response = {
            'document_id': doc_id,
            'mode': mode,
            'language': language,
            'tasks': selected_tasks,
            'quality_score': quality_score,
            'duration': duration,
            'task_results': task_results,
            'timestamp': (datetime.now().replace(
                hour=random.randint(0, 23),
                minute=random.randint(0, 59),
                second=random.randint(0, 59)
            )).isoformat()
        }
        
        mock_responses.append(response)
        
    return mock_responses

def main():
    """Main entry point for the example script."""
    parser = argparse.ArgumentParser(description='LLM Response Visualization Example')
    parser.add_argument('--results-dir', type=str, default='results/llm_responses',
                       help='Directory for storing and reading response data')
    parser.add_argument('--output-dir', type=str, default='results/visualizations',
                       help='Directory for saving visualization outputs')
    parser.add_argument('--mock-data', action='store_true',
                       help='Generate and use mock data')
    parser.add_argument('--mock-count', type=int, default=30,
                       help='Number of mock responses to generate')
    parser.add_argument('--clean', action='store_true',
                       help='Clean old results (30+ days)')
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting LLM Response Visualization Example")
    
    # Initialize the response collector
    collector = ResponseCollector(storage_dir=args.results_dir)
    
    # Clean old results if requested
    if args.clean:
        deleted = collector.clear_old_responses()
        logger.info(f"Cleaned {deleted} old response files")
    
    # Generate and save mock data if requested
    if args.mock_data:
        mock_responses = create_mock_responses(args.mock_count)
        logger.info(f"Generated {len(mock_responses)} mock responses")
        
        paths = collector.save_batch_responses(mock_responses)
        logger.info(f"Saved mock responses to {len(paths)} files")
    
    # Initialize the visualizer
    visualizer = LLMResponseAnalyzer(
        results_dir=args.results_dir,
        output_dir=args.output_dir
    )
    
    # Load response data
    record_count = visualizer.load_response_data()
    logger.info(f"Loaded {record_count} response records")
    
    if record_count == 0:
        logger.error("No response data found. Use --mock-data to generate sample data.")
        return 1
    
    # Generate visualizations
    viz_paths = visualizer.generate_all_visualizations()
    
    if viz_paths:
        logger.info("Generated visualizations:")
        for viz_type, path in viz_paths.items():
            logger.info(f"  {viz_type}: {path}")
    
    # Generate and display summary statistics
    stats = visualizer.generate_summary_statistics()
    
    if stats:
        logger.info("Summary Statistics:")
        print(json.dumps(stats, indent=2, default=str))
    
    logger.info("Visualization example completed")
    return 0

if __name__ == '__main__':
    sys.exit(main())
