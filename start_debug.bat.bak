@echo off
echo === 启动调试页面 ===
echo.

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR - Python未安装或不在PATH中
    pause
    exit /b 1
)

echo OK - Python已安装
echo.

REM 进入前端目录
cd frontend

echo 正在启动前端服务器...
echo 服务器地址: http://localhost:8089
echo 调试页面: http://localhost:8089/debug.html
echo.

REM 启动服务器
start python -m http.server 8089

echo 等待服务器启动...
timeout /t 3 /nobreak >nul

REM 打开浏览器
start http://localhost:8089/debug.html

echo.
echo 服务器已启动！
echo 请在浏览器中测试文件选择功能
echo 按 Ctrl+C 停止服务器
echo.

pause