# Task 1: Actor and Relationship Extraction

**System Instruction:** You are an information extraction model. Read the input document and the provided definitions. Your task is to extract all actors and their pairwise relationships. Return ONLY a valid JSON object matching the Output Schema. Do not include explanations.

**Referenced Definitions:** Please adhere to all rules in `prompt_core_definitions_en.md`.

## Task-Specific Goal

-   Identify all real persons and organizations (`actors`).
-   Identify all directed or undirected interactions between pairs of actors (`relationships`).
-   Map each actor to appropriate stakeholder category.
-   For relationships, identify character roles if present.

## Detailed Instructions

### Step 1: Actor Extraction
1. Scan document for all named entities that are real persons or organizations
2. Exclude generic terms unless they appear as direct quotes
3. For each actor, determine the most appropriate stakeholder category
4. Add brief notes only when clarification is essential

### Step 2: Relationship Extraction  
1. Identify all statements describing interactions between actors
2. Convert multi-party interactions into pairwise relationships
3. Preserve directionality ("A regulates B" is different from "B regulates A")
4. Extract supporting evidence as direct quotes when possible
5. Assign character roles (hero/villain/victim) only when explicitly framed

## Output Schema

```json
{
  "doc_id": "string",
  "title": "string|null",
  "actors": [
    {
      "name": "string",
      "stakeholder_category": "Think Tanks|Government|Media|Corporate|NGOs and Professional Organizations|Universities and Research Institutes|Political Entities|Consultants and Analysts|Legal and Industry-specific Bodies|State Guidelines and Documents|Others",
      "notes": "string|null"
    }
  ],
  "relationships": {
    "skipped": false,
    "skip_reason": "string|null",
    "items": [
      {
        "a_name": "string",
        "b_name": "string",
        "relationship": "string", 
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null",
        "a_type": "stakeholder category from list|Others",
        "b_type": "stakeholder category from list|Others",
        "evidence": "quote or concise span from text"
      }
    ]
  }
}
```

## Examples

### Example Input
```text
doc_id: "doc_001"
title: "AI Regulation Debate"
text: "The FTC announced new guidelines for AI companies. OpenAI CEO Sam Altman criticized the rules as stifling innovation, while Senator Warren praised them as necessary consumer protections. Microsoft and Google have formed a coalition to lobby against the regulations."
optional_known_actors: ["FTC", "OpenAI"]
```

### Example Output
```json
{
  "doc_id": "doc_001",
  "title": "AI Regulation Debate",
  "actors": [
    {
      "name": "FTC",
      "stakeholder_category": "Government",
      "notes": null
    },
    {
      "name": "OpenAI",
      "stakeholder_category": "Corporate",
      "notes": null
    },
    {
      "name": "Sam Altman",
      "stakeholder_category": "Corporate",
      "notes": "CEO of OpenAI"
    },
    {
      "name": "Senator Warren",
      "stakeholder_category": "Political Entities",
      "notes": null
    },
    {
      "name": "Microsoft",
      "stakeholder_category": "Corporate",
      "notes": null
    },
    {
      "name": "Google",
      "stakeholder_category": "Corporate",
      "notes": null
    }
  ],
  "relationships": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "a_name": "Sam Altman",
        "b_name": "FTC",
        "relationship": "criticized",
        "a_character": null,
        "b_character": null,
        "a_type": "Corporate",
        "b_type": "Government",
        "evidence": "OpenAI CEO Sam Altman criticized the rules as stifling innovation"
      },
      {
        "a_name": "Senator Warren",
        "b_name": "FTC",
        "relationship": "praised",
        "a_character": null,
        "b_character": "hero",
        "a_type": "Political Entities",
        "b_type": "Government",
        "evidence": "Senator Warren praised them as necessary consumer protections"
      },
      {
        "a_name": "Microsoft",
        "b_name": "Google",
        "relationship": "formed coalition with",
        "a_character": null,
        "b_character": null,
        "a_type": "Corporate",
        "b_type": "Corporate",
        "evidence": "Microsoft and Google have formed a coalition"
      },
      {
        "a_name": "Microsoft",
        "b_name": "FTC",
        "relationship": "lobbies against",
        "a_character": null,
        "b_character": null,
        "a_type": "Corporate",
        "b_type": "Government",
        "evidence": "Microsoft and Google have formed a coalition to lobby against the regulations"
      },
      {
        "a_name": "Google",
        "b_name": "FTC",
        "relationship": "lobbies against",
        "a_character": null,
        "b_character": null,
        "a_type": "Corporate",
        "b_type": "Government",
        "evidence": "Microsoft and Google have formed a coalition to lobby against the regulations"
      }
    ]
  }
}
```

## Common Patterns to Watch For

1. **Coalition Formation**: "A, B, and C formed an alliance" → Extract pairwise collaborations
2. **Regulatory Actions**: "X regulates Y" → One-way relationship
3. **Public Statements**: "X criticized/praised/warned Y" → Extract with evidence
4. **Financial Relationships**: "X funds Y" → Clear directional relationship
5. **Legal Actions**: "X sued Y" → One-way relationship

## Input Parameters

- `doc_id`: string (unique identifier for the document)
- `title`: string or null (document title if available)
- `text`: string (the document body to analyze)
- `optional_known_actors`: array of strings (pre-identified actors to ensure consistency)

## Error Prevention

- Ensure all actors in relationships exist in actors array
- Use exact names as they appear in the document
- Don't infer relationships not explicitly stated
- Set character roles only when clearly framed in text
- If no relationships found, set skipped=true with appropriate reason
