{"openapi": "3.1.0", "info": {"title": "Document Analysis System API", "description": "RESTful API for document analysis and prompt testing", "version": "1.0.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "Root endpoint with API information.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Root  Get"}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/config": {"get": {"summary": "Get Config", "description": "Get current configuration.", "operationId": "get_config_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Config Config Get"}}}}}}, "post": {"summary": "Update Config", "description": "Update configuration.", "operationId": "update_config_config_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Update Config Config Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/analyze": {"post": {"summary": "Analyze Document", "description": "Analyze a single document.", "operationId": "analyze_document_analyze_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/analyze/upload": {"post": {"summary": "Analyze Uploaded File", "description": "Analyze an uploaded file.", "operationId": "analyze_uploaded_file_analyze_upload_post", "parameters": [{"name": "template_name", "in": "query", "required": false, "schema": {"type": "string", "default": "unified_analysis", "title": "Template Name"}}, {"name": "doc_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Doc Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_analyze_uploaded_file_analyze_upload_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/batch": {"post": {"summary": "<PERSON><PERSON><PERSON>", "description": "Analyze multiple documents in batch.", "operationId": "analyze_batch_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchAnalysisRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DocumentResponse"}, "type": "array", "title": "Response Analyze Batch Batch Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/test-prompt": {"post": {"summary": "Test Prompt", "description": "Test a prompt template.", "operationId": "test_prompt_test_prompt_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromptTestRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/templates": {"get": {"summary": "Get Templates", "description": "Get available prompt templates.", "operationId": "get_templates_templates_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/templates/{template_name}": {"get": {"summary": "Get Template", "description": "Get specific prompt template.", "operationId": "get_template_templates__template_name__get", "parameters": [{"name": "template_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Template Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/metrics": {"get": {"summary": "Get Metrics", "description": "Get system metrics.", "operationId": "get_metrics_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/test-llm": {"post": {"summary": "Test Llm", "description": "Test LLM connection.", "operationId": "test_llm_test_llm_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"BatchAnalysisRequest": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/DocumentRequest"}, "type": "array", "title": "Documents"}, "template_name": {"type": "string", "title": "Template Name", "default": "unified_analysis"}}, "type": "object", "required": ["documents"], "title": "BatchAnalysisRequest"}, "Body_analyze_uploaded_file_analyze_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_analyze_uploaded_file_analyze_upload_post"}, "ConfigRequest": {"properties": {"llm_provider": {"type": "string", "title": "Llm Provider", "default": "openai"}, "llm_model": {"type": "string", "title": "Llm Model", "default": "gpt-4"}, "temperature": {"type": "number", "title": "Temperature", "default": 0.0}, "max_tokens": {"type": "integer", "title": "<PERSON>", "default": 4000}, "use_mock_llm": {"type": "boolean", "title": "Use Mock Llm", "default": false}, "min_confidence_score": {"type": "number", "title": "Min Confidence Score", "default": 0.7}, "max_document_length": {"type": "integer", "title": "Max Document Length", "default": 10000}}, "type": "object", "title": "ConfigRequest"}, "DocumentRequest": {"properties": {"doc_id": {"type": "string", "title": "Doc Id"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "text": {"type": "string", "title": "Text"}, "known_actors": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Known Actors"}, "template_name": {"type": "string", "title": "Template Name", "default": "unified_analysis"}}, "type": "object", "required": ["doc_id", "text"], "title": "DocumentRequest"}, "DocumentResponse": {"properties": {"doc_id": {"type": "string", "title": "Doc Id"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "analysis": {"type": "object", "title": "Analysis"}, "quality_score": {"type": "number", "title": "Quality Score"}, "execution_time": {"type": "number", "title": "Execution Time"}, "confidence_scores": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Confidence Scores"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["doc_id", "analysis", "quality_score", "execution_time", "confidence_scores", "warnings", "metadata"], "title": "DocumentResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "timestamp": {"type": "string", "title": "Timestamp"}, "version": {"type": "string", "title": "Version"}, "config_loaded": {"type": "boolean", "title": "Config Loaded"}, "llm_ready": {"type": "boolean", "title": "Llm Ready"}}, "type": "object", "required": ["status", "timestamp", "version", "config_loaded", "llm_ready"], "title": "HealthResponse"}, "PromptTestRequest": {"properties": {"template_name": {"type": "string", "title": "Template Name"}, "document_id": {"type": "string", "title": "Document Id"}, "document_text": {"type": "string", "title": "Document Text"}, "document_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Title"}, "variables": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Variables"}}, "type": "object", "required": ["template_name", "document_id", "document_text"], "title": "PromptTestRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}