<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档分析测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🔧 文档分析功能测试</h1>
        
        <div class="test-panel">
            <h3>文件选择测试</h3>
            <div class="mb-3">
                <input type="file" id="testFileInput" accept=".txt,.pdf,.docx,.doc,.xlsx,.xls,.csv" multiple>
                <button class="btn btn-primary ms-2" onclick="testFileSelection()">测试文件选择</button>
            </div>
            <div id="fileSelectionResult" class="alert alert-info" style="display: none;"></div>
        </div>
        
        <div class="test-panel">
            <h3>步骤导航测试</h3>
            <div class="mb-3">
                <button class="btn btn-success" onclick="testNextStep()">测试下一步</button>
                <button class="btn btn-warning" onclick="testPreviousStep()">测试上一步</button>
                <button class="btn btn-info" onclick="testVariables()">测试变量</button>
            </div>
            <div id="stepTestResult" class="alert alert-secondary" style="display: none;"></div>
        </div>
        
        <div class="test-panel">
            <h3>API连接测试</h3>
            <div class="mb-3">
                <button class="btn btn-primary" onclick="testAPIConnection()">测试API连接</button>
                <button class="btn btn-secondary" onclick="testTemplates()">测试模板加载</button>
            </div>
            <div id="apiTestResult" class="alert alert-info" style="display: none;"></div>
        </div>
        
        <div class="test-panel">
            <h3>控制台输出</h3>
            <div id="consoleOutput" class="console-output">等待测试...</div>
            <button class="btn btn-danger" onclick="clearConsole()">清空控制台</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedDocument = null;
        let selectedFiles = [];
        let fileContents = {};
        let currentStep = 1;
        const API_BASE_URL = 'http://localhost:8006';
        
        // 控制台输出
        function log(message) {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.textContent += `[${timestamp}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').textContent = '';
        }
        
        // 测试文件选择
        function testFileSelection() {
            const fileInput = document.getElementById('testFileInput');
            const files = fileInput.files;
            
            log(`测试文件选择 - 选择了 ${files.length} 个文件`);
            
            if (files.length === 0) {
                showResult('fileSelectionResult', '请先选择文件', 'warning');
                return;
            }
            
            selectedFiles = [];
            fileContents = {};
            
            // 处理每个文件
            Array.from(files).forEach((file, index) => {
                const fileId = `file_${index}_${Date.now()}`;
                const fileInfo = {
                    id: fileId,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    file: file
                };
                
                selectedFiles.push(fileInfo);
                log(`添加文件: ${file.name} (${formatFileSize(file.size)})`);
                
                // 读取文件内容
                readFileContent(file, fileId);
            });
            
            // 默认选择第一个文件
            if (selectedFiles.length > 0) {
                selectedDocument = selectedFiles[0];
                log(`自动选择文件: ${selectedDocument.name}`);
            }
            
            showResult('fileSelectionResult', `成功选择 ${files.length} 个文件`, 'success');
        }
        
        // 读取文件内容
        function readFileContent(file, fileId) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                let content = e.target.result;
                
                // 根据文件类型处理内容
                if (file.type === 'application/pdf') {
                    content = `PDF文件: ${file.name}\\n大小: ${formatFileSize(file.size)}`;
                } else if (file.name.match(/\.(doc|docx)$/)) {
                    content = `Word文档: ${file.name}\\n大小: ${formatFileSize(file.size)}`;
                } else if (file.name.match(/\.(xls|xlsx|csv)$/)) {
                    content = `Excel文件: ${file.name}\\n大小: ${formatFileSize(file.size)}`;
                } else {
                    // 文本文件，直接显示内容（限制长度）
                    if (content.length > 1000) {
                        content = content.substring(0, 1000) + '\\n... [内容过长，只显示前1000字符]';
                    }
                }
                
                fileContents[fileId] = content;
                log(`文件内容已读取: ${file.name} (${content.length} 字符)`);
            };
            
            reader.onerror = function() {
                log(`文件读取失败: ${file.name}`);
            };
            
            // 根据文件类型选择读取方式
            if (file.type.startsWith('text/') || file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsDataURL(file);
            }
        }
        
        // 测试下一步
        function testNextStep() {
            log(`测试下一步 - 当前步骤: ${currentStep}, 选中文档: ${selectedDocument ? selectedDocument.name : '无'}`);
            
            if (currentStep === 1 && !selectedDocument) {
                log('错误: 步骤1需要先选择文档');
                showResult('stepTestResult', '请先选择一个文档', 'danger');
                return;
            }
            
            // 模拟步骤切换
            log(`从步骤 ${currentStep} 切换到步骤 ${currentStep + 1}`);
            currentStep++;
            
            showResult('stepTestResult', `成功切换到步骤 ${currentStep}`, 'success');
        }
        
        // 测试上一步
        function testPreviousStep() {
            log(`测试上一步 - 当前步骤: ${currentStep}`);
            
            if (currentStep > 1) {
                log(`从步骤 ${currentStep} 切换到步骤 ${currentStep - 1}`);
                currentStep--;
                showResult('stepTestResult', `成功切换到步骤 ${currentStep}`, 'success');
            } else {
                log('错误: 已经是第一步');
                showResult('stepTestResult', '已经是第一步', 'warning');
            }
        }
        
        // 测试变量
        function testVariables() {
            log('测试变量状态...');
            log(`selectedDocument: ${JSON.stringify(selectedDocument, null, 2)}`);
            log(`selectedFiles: ${selectedFiles.length} 个文件`);
            log(`fileContents: ${Object.keys(fileContents).length} 个内容`);
            log(`currentStep: ${currentStep}`);
            
            showResult('stepTestResult', '变量状态已输出到控制台', 'info');
        }
        
        // 测试API连接
        async function testAPIConnection() {
            log('测试API连接...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                log(`API连接成功: ${JSON.stringify(data)}`);
                showResult('apiTestResult', 'API连接成功', 'success');
            } catch (error) {
                log(`API连接失败: ${error.message}`);
                showResult('apiTestResult', `API连接失败: ${error.message}`, 'danger');
            }
        }
        
        // 测试模板加载
        async function testTemplates() {
            log('测试模板加载...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/templates`);
                const data = await response.json();
                
                log(`模板加载成功: ${JSON.stringify(data)}`);
                showResult('apiTestResult', '模板加载成功', 'success');
            } catch (error) {
                log(`模板加载失败: ${error.message}`);
                showResult('apiTestResult', `模板加载失败: ${error.message}`, 'danger');
            }
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `alert alert-${type}`;
            element.textContent = message;
            element.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                element.style.display = 'none';
            }, 3000);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载');
            log('API_BASE_URL: ' + API_BASE_URL);
        });
    </script>
</body>
</html>