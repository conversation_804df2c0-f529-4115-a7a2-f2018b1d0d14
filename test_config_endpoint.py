#!/usr/bin/env python3
"""
Direct test of the config endpoint
"""

import requests
import json

# Test the config endpoint
try:
    response = requests.get("http://localhost:8006/config")
    config_data = response.json()
    
    print("=== Config Endpoint Test ===")
    print(json.dumps(config_data, indent=2))
    print("============================")
    
    # Check specific values
    provider = config_data.get('llm', {}).get('provider')
    model = config_data.get('llm', {}).get('model')
    use_mock = config_data.get('use_mock_llm')
    
    print(f"Provider: {provider}")
    print(f"Model: {model}")
    print(f"Use Mock: {use_mock}")
    
    if provider == 'zhipuai' and model == 'glm-4' and use_mock == False:
        print("SUCCESS: Configuration is correct!")
    else:
        print("ERROR: Configuration is incorrect!")
        
except Exception as e:
    print(f"Error testing config endpoint: {e}")