/* 主要CSS样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.chart-container {
    position: relative;
    height: 350px;
    margin-bottom: 20px;
}

.stat-card {
    transition: all 0.3s ease;
    text-align: center;
    padding: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 10px 0;
}

.quality-critical { color: #dc3545; }
.quality-low { color: #fd7e14; }
.quality-moderate { color: #ffc107; }
.quality-good { color: #198754; }

/* 自定义表格样式 */
.table {
    font-size: 0.9rem;
}

.table thead th {
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.03);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
    
    .stat-card h2 {
        font-size: 2rem;
    }
}
