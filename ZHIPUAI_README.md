# 智谱API集成说明

## 概述

文档分析工作台现已支持智谱AI API，默认配置为使用智谱AI的GLM-4模型。

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中配置以下参数：

```bash
# 智谱AI API配置
LLM_PROVIDER=zhipuai
LLM_MODEL=glm-4
USE_MOCK_LLM=false

# 智谱AI API密钥
ZHIPUAI_API_KEY=your_api_key_here
```

### 2. 获取API密钥

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册并登录账号
3. 在控制台中获取API密钥
4. 将API密钥填入 `.env` 文件中的 `ZHIPUAI_API_KEY` 字段

### 3. 支持的模型

目前支持以下智谱AI模型：
- `glm-4` (默认)
- `glm-4-air`
- `glm-4-flash`
- `glm-3-turbo`

## 使用方法

### 1. 启动Web API服务

```bash
# 启动服务
python web_api.py

# 或使用调试模式
python debug_server.py
```

### 2. 使用命令行工具

```bash
# 测试智谱AI集成
python test_zhipuai.py

# 运行文档分析
python start_workflow.py
```

### 3. API请求示例

```python
import requests

# 分析文档
response = requests.post("http://localhost:8006/analyze", json={
    "doc_id": "test_doc",
    "title": "测试文档",
    "text": "文档内容...",
    "template_name": "unified_analysis"
})

print(response.json())
```

## 测试验证

### 1. 运行集成测试

```bash
# 使用模拟模式测试
python test_zhipuai.py

# 使用真实API测试（需要配置API密钥）
# 设置 ZHIPUAI_API_KEY 环境变量后运行
python test_zhipuai.py
```

### 2. 验证API连接

```bash
# 启动服务后访问健康检查
curl http://localhost:8006/health

# 测试LLM连接
curl -X POST http://localhost:8006/test-llm
```

## 错误处理

### 常见错误

1. **API密钥未配置**
   - 错误信息：`No API key found`
   - 解决方案：在 `.env` 文件中设置 `ZHIPUAI_API_KEY`

2. **网络连接问题**
   - 错误信息：`ZhipuAI API error: ConnectionError`
   - 解决方案：检查网络连接和代理设置

3. **API配额耗尽**
   - 错误信息：`ZhipuAI API error: 429`
   - 解决方案：检查账户余额和配额

### 调试模式

启用调试模式以获取更详细的错误信息：

```bash
# 在 .env 文件中设置
DEBUG_MODE=true
```

## 性能优化

### 1. 模型选择

- `glm-4`：质量最高，速度较慢
- `glm-4-air`：平衡质量和速度
- `glm-4-flash`：速度最快，适合简单任务

### 2. 参数调优

```bash
# 调整温度参数
LLM_TEMPERATURE=0.7

# 调整最大输出长度
LLM_MAX_TOKENS=2000

# 调整超时时间
LLM_TIMEOUT=60
```

## 注意事项

1. **API费用**：使用智谱AI API会产生费用，请合理使用
2. **数据隐私**：文档内容会发送到智谱AI服务器进行处理
3. **速率限制**：请注意API的调用频率限制
4. **模型更新**：智谱AI可能会更新模型，请关注官方通知

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. API密钥是否正确配置
3. 账户余额是否充足
4. 模型是否可用

更多信息请参考：
- [智谱AI官方文档](https://open.bigmodel.cn/docs/)
- [API错误码说明](https://open.bigmodel.cn/docs/api/error-code)