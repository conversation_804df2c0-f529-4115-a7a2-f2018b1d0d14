#!/usr/bin/env python3
"""
Test file selection functionality
"""

import os
import sys
from pathlib import Path

def test_frontend_files():
    """Test that frontend files exist and are accessible"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("ERROR: Frontend directory not found")
        return False
    
    required_files = ["debug.html", "index.html", "test.html"]
    
    for file in required_files:
        file_path = frontend_dir / file
        if not file_path.exists():
            print(f"ERROR: {file} not found")
            return False
        else:
            print(f"OK: {file} found ({file_path.stat().st_size} bytes)")
    
    return True

def test_debug_page_content():
    """Test debug page content for file selection functionality"""
    debug_file = Path("frontend/debug.html")
    
    if not debug_file.exists():
        print("ERROR: debug.html not found")
        return False
    
    content = debug_file.read_text(encoding='utf-8')
    
    # Check for key elements
    required_elements = [
        'fileInput',
        'handleFileSelect',
        'selectDocument',
        'selectedFilesList',
        'documentPreview',
        'nextStep1'
    ]
    
    for element in required_elements:
        if element in content:
            print(f"OK: {element} found in debug.html")
        else:
            print(f"ERROR: {element} not found in debug.html")
            return False
    
    return True

def main():
    print("=== Testing File Selection Functionality ===")
    print()
    
    # Test frontend files
    if not test_frontend_files():
        print("FAILED: Frontend files test")
        return False
    
    print()
    
    # Test debug page content
    if not test_debug_page_content():
        print("FAILED: Debug page content test")
        return False
    
    print()
    print("SUCCESS: All tests passed!")
    print()
    print("To test the file selection functionality:")
    print("1. Start the frontend server: python -m http.server 8089")
    print("2. Open browser to: http://localhost:8089/debug.html")
    print("3. Try selecting files using the file input")
    print("4. Check the console output for debugging information")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)