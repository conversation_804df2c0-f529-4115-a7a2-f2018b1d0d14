"""
Document reading utilities for various file formats.
"""

import os
import json
import csv
import pdfplumber
import docx
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Iterator
from dataclasses import dataclass
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Document:
    """Data class representing a document."""
    doc_id: str
    title: Optional[str] = None
    text: str = ""
    file_path: Optional[str] = None
    file_type: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class DocumentReader:
    """Handles reading documents from various file formats."""
    
    SUPPORTED_FORMATS = {
        '.txt': 'text',
        '.pdf': 'pdf',
        '.docx': 'docx',
        '.csv': 'csv',
        '.json': 'json',
        '.md': 'markdown',
        '.html': 'html',
        '.htm': 'html'
    }
    
    def __init__(self, encoding: str = 'utf-8'):
        self.encoding = encoding
    
    def read_file(self, file_path: Union[str, Path]) -> Document:
        """Read a single file and return a Document object."""
        path = Path(file_path)
        
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_extension = path.suffix.lower()
        if file_extension not in self.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        doc_id = path.stem
        title = path.stem.replace('_', ' ').replace('-', ' ').title()
        
        try:
            if file_extension == '.txt':
                text = self._read_text_file(path)
            elif file_extension == '.pdf':
                text = self._read_pdf_file(path)
            elif file_extension == '.docx':
                text = self._read_docx_file(path)
            elif file_extension == '.csv':
                text = self._read_csv_file(path)
            elif file_extension == '.json':
                text = self._read_json_file(path)
            elif file_extension in ['.md', '.html', '.htm']:
                text = self._read_text_file(path)
            else:
                raise ValueError(f"Unsupported file format: {file_extension}")
            
            metadata = {
                'file_size': path.stat().st_size,
                'file_modified': path.stat().st_mtime,
                'file_extension': file_extension,
                'char_count': len(text),
                'word_count': len(text.split()) if text else 0
            }
            
            return Document(
                doc_id=doc_id,
                title=title,
                text=text,
                file_path=str(path),
                file_type=file_extension,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            raise
    
    def _read_text_file(self, path: Path) -> str:
        """Read a text file."""
        try:
            with open(path, 'r', encoding=self.encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['utf-8', 'latin-1', 'cp1252', 'gbk', 'gb2312']:
                try:
                    with open(path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"Could not decode file {path} with any supported encoding")
    
    def _read_pdf_file(self, path: Path) -> str:
        """Read a PDF file."""
        text_parts = []
        try:
            with pdfplumber.open(path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(page_text)
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"Error reading PDF {path}: {str(e)}")
            raise
    
    def _read_docx_file(self, path: Path) -> str:
        """Read a DOCX file."""
        try:
            doc = docx.Document(path)
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text:
                    text_parts.append(paragraph.text)
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"Error reading DOCX {path}: {str(e)}")
            raise
    
    def _read_csv_file(self, path: Path) -> str:
        """Read a CSV file and convert to text."""
        try:
            df = pd.read_csv(path)
            # Convert DataFrame to formatted text
            text = f"CSV File: {path.name}\n"
            text += f"Columns: {', '.join(df.columns.tolist())}\n"
            text += f"Rows: {len(df)}\n\n"
            
            # Add data preview
            if len(df) > 0:
                text += "Data Preview:\n"
                for i, row in df.head(10).iterrows():
                    text += f"Row {i+1}: {row.to_dict()}\n"
                
                if len(df) > 10:
                    text += f"... and {len(df) - 10} more rows\n"
            
            return text
        except Exception as e:
            logger.error(f"Error reading CSV {path}: {str(e)}")
            raise
    
    def _read_json_file(self, path: Path) -> str:
        """Read a JSON file and convert to text."""
        try:
            with open(path, 'r', encoding=self.encoding) as f:
                data = json.load(f)
            
            # Convert to formatted text
            if isinstance(data, dict):
                text = f"JSON File: {path.name}\n\n"
                text += "Contents:\n"
                text += json.dumps(data, indent=2, ensure_ascii=False)
            elif isinstance(data, list):
                text = f"JSON File: {path.name} (Array)\n\n"
                text += f"Items: {len(data)}\n\n"
                text += "Contents:\n"
                text += json.dumps(data, indent=2, ensure_ascii=False)
            else:
                text = str(data)
            
            return text
        except Exception as e:
            logger.error(f"Error reading JSON {path}: {str(e)}")
            raise
    
    def read_directory(self, directory: Union[str, Path], recursive: bool = True) -> List[Document]:
        """Read all supported files from a directory."""
        dir_path = Path(directory)
        
        if not dir_path.exists():
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        if not dir_path.is_dir():
            raise ValueError(f"Path is not a directory: {directory}")
        
        documents = []
        
        # Get all files
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
        
        for file_path in dir_path.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in self.SUPPORTED_FORMATS:
                try:
                    doc = self.read_file(file_path)
                    documents.append(doc)
                    logger.info(f"Read file: {file_path}")
                except Exception as e:
                    logger.warning(f"Could not read file {file_path}: {str(e)}")
                    continue
        
        # Sort by filename
        documents.sort(key=lambda x: x.file_path or "")
        
        return documents
    
    def read_multiple_files(self, file_paths: List[Union[str, Path]]) -> List[Document]:
        """Read multiple files from a list of paths."""
        documents = []
        
        for file_path in file_paths:
            try:
                doc = self.read_file(file_path)
                documents.append(doc)
                logger.info(f"Read file: {file_path}")
            except Exception as e:
                logger.warning(f"Could not read file {file_path}: {str(e)}")
                continue
        
        return documents
    
    def filter_documents_by_size(self, documents: List[Document], 
                               min_chars: int = 100, 
                               max_chars: int = 50000) -> List[Document]:
        """Filter documents by size."""
        filtered = []
        for doc in documents:
            if min_chars <= len(doc.text) <= max_chars:
                filtered.append(doc)
            else:
                logger.info(f"Filtered document {doc.doc_id}: {len(doc.text)} characters (not in range {min_chars}-{max_chars})")
        
        return filtered
    
    def get_document_stats(self, documents: List[Document]) -> Dict[str, Any]:
        """Get statistics about a collection of documents."""
        if not documents:
            return {}
        
        total_chars = sum(len(doc.text) for doc in documents)
        total_words = sum(len(doc.text.split()) for doc in documents)
        
        file_types = {}
        for doc in documents:
            file_type = doc.file_type or 'unknown'
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        return {
            'total_documents': len(documents),
            'total_characters': total_chars,
            'total_words': total_words,
            'average_chars_per_doc': total_chars / len(documents),
            'average_words_per_doc': total_words / len(documents),
            'file_types': file_types,
            'char_count_range': {
                'min': min(len(doc.text) for doc in documents),
                'max': max(len(doc.text) for doc in documents)
            }
        }

class BatchDocumentProcessor:
    """Processes documents in batches with memory management."""
    
    def __init__(self, batch_size: int = 10, max_memory_mb: int = 1024):
        self.batch_size = batch_size
        self.max_memory_mb = max_memory_mb
        self.reader = DocumentReader()
    
    def process_directory(self, directory: Union[str, Path], 
                         output_dir: Optional[Union[str, Path]] = None) -> Iterator[List[Document]]:
        """Process documents in directory in batches."""
        documents = self.reader.read_directory(directory)
        
        for i in range(0, len(documents), self.batch_size):
            batch = documents[i:i + self.batch_size]
            
            # Save batch if output directory is specified
            if output_dir:
                self._save_batch(batch, output_dir, i)
            
            yield batch
    
    def _save_batch(self, batch: List[Document], output_dir: Union[str, Path], batch_index: int) -> None:
        """Save a batch of documents to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        batch_file = output_path / f"batch_{batch_index:03d}.json"
        
        batch_data = []
        for doc in batch:
            doc_dict = {
                'doc_id': doc.doc_id,
                'title': doc.title,
                'text': doc.text,
                'file_path': doc.file_path,
                'file_type': doc.file_type,
                'metadata': doc.metadata
            }
            batch_data.append(doc_dict)
        
        with open(batch_file, 'w', encoding='utf-8') as f:
            json.dump(batch_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved batch {batch_index} with {len(batch)} documents to {batch_file}")

def create_sample_document() -> Document:
    """Create a sample document for testing."""
    sample_text = """
    The Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies 
    like Google and Microsoft. According to the FTC, these companies have been engaging in 
    anti-competitive practices that harm consumers and stifle innovation. 
    
    Google spokesperson Jane Smith defended the company's practices, stating that they 
    "operate within the law and promote healthy competition." However, consumer advocacy 
    groups argue that the regulations don't go far enough to protect users from 
    monopolistic behavior.
    
    Microsoft CEO Satya Nadella called for a balanced approach that protects consumers 
    while encouraging technological innovation. The debate continues as policymakers 
    consider the broader implications for the tech industry.
    """
    
    return Document(
        doc_id="sample_001",
        title="FTC Tech Regulations",
        text=sample_text.strip(),
        metadata={"sample": True, "purpose": "testing"}
    )