#!/usr/bin/env python3
"""
Test script to verify API configuration and endpoints
"""

import os
import sys
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_endpoints():
    """Test API endpoints"""
    base_url = "http://localhost:8006"
    
    print("Testing API endpoints...")
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health")
        health_data = response.json()
        print(f"PASS: Health endpoint: {health_data}")
    except Exception as e:
        print(f"FAIL: Health endpoint failed: {e}")
        return False
    
    # Test config endpoint
    try:
        response = requests.get(f"{base_url}/config")
        config_data = response.json()
        print("PASS: Config endpoint loaded")
        print(f"  - Provider: {config_data['llm']['provider']}")
        print(f"  - Model: {config_data['llm']['model']}")
        print(f"  - Use Mock: {config_data['use_mock_llm']}")
        print(f"  - API Key set: {bool(config_data['llm']['api_key'])}")
    except Exception as e:
        print(f"FAIL: Config endpoint failed: {e}")
        return False
    
    return True

def test_local_config():
    """Test local configuration loading"""
    print("\nTesting local configuration...")
    
    # Test environment variables
    print(f"ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
    print(f"LLM_PROVIDER: {os.getenv('LLM_PROVIDER', 'NOT SET')}")
    print(f"USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM', 'NOT SET')}")
    
    # Test Config class
    try:
        from src.core.config import Config
        config = Config()
        print("PASS: Config loaded successfully")
        print(f"  - Provider: {config.llm.provider}")
        print(f"  - Model: {config.llm.model}")
        print(f"  - API Key: {'SET' if config.llm.api_key else 'NOT SET'}")
        print(f"  - Use Mock: {config.use_mock_llm}")
        return True
    except Exception as e:
        print(f"FAIL: Config loading failed: {e}")
        return False

if __name__ == "__main__":
    print("=== API Configuration Test ===\n")
    
    # Test local configuration
    local_ok = test_local_config()
    
    # Test API endpoints
    api_ok = test_api_endpoints()
    
    print(f"\n=== Results ===")
    print(f"Local Configuration: {'PASS' if local_ok else 'FAIL'}")
    print(f"API Endpoints: {'PASS' if api_ok else 'FAIL'}")
    
    if local_ok and api_ok:
        print("\nSUCCESS: All tests passed! The UI should display the correct configuration.")
    else:
        print("\nERROR: Some tests failed. Check the configuration.")