{"unified_analysis": {"success": true, "execution_time": 0.10017824172973633, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "actor_extraction": {"success": true, "execution_time": 0.10086870193481445, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "relationship_analysis": {"success": true, "execution_time": 0.10046052932739258, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "portrayal_analysis": {"success": true, "execution_time": 0.10033488273620605, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "issue_scope_analysis": {"success": true, "execution_time": 0.10064554214477539, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "causal_mechanism_analysis": {"success": true, "execution_time": 0.10074543952941895, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "openai_optimized": {"success": true, "execution_time": 0.10089564323425293, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "anthropic_optimized": {"success": true, "execution_time": 0.10035371780395508, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}, "zhipu_optimized": {"success": true, "execution_time": 0.10057950019836426, "quality_score": 0.1, "warnings": ["Low confidence in actors analysis", "Low confidence in relationships analysis", "Low confidence in portrayals analysis", "Low confidence in issue scope analysis", "Low confidence in causal mechanisms analysis"], "analysis_keys": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"], "actors_count": 1, "relationships_count": 0, "portrayals_count": 0, "issue_scope_count": 0, "causal_mechanisms_count": 0}}