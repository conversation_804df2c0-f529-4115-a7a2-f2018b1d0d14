"""
Modular document analyzer with task-specific analysis.
"""

import json
import time
import logging
from typing import Dict, Any, List, Set, Optional
from src.core.cache_manager import CacheManager
from dataclasses import dataclass, field
from pathlib import Path

from .config import Config
from .analyzer import DocumentAnalyzer, AnalysisResult
from .modular_prompts import (
    ModularPromptSystem,
    AnalysisMode,
    TaskType,
    AnalysisContext
)
from ..utils.document_reader import Document

logger = logging.getLogger(__name__)

@dataclass
class ModularAnalysisResult(AnalysisResult):
    """Extended result for modular analysis."""
    mode: AnalysisMode = AnalysisMode.UNIFIED
    task_results: Dict[TaskType, Dict[str, Any]] = field(default_factory=dict)
    task_timings: Dict[TaskType, float] = field(default_factory=dict)

class ModularDocumentAnalyzer(DocumentAnalyzer):
    """Analyzer supporting modular prompt system."""
    
    def __init__(self, config: Config, llm_client=None, use_cache=True):
        """Initialize modular analyzer."""
        super().__init__(config)
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.modular_prompts = ModularPromptSystem()
        self.logger.info(f"Loaded {len(self.modular_prompts.task_prompts)} modular prompts")
        
        # Initialize cache manager
        self.use_cache = use_cache
        self.cache_manager = CacheManager(cache_dir="cache/modular") if use_cache else None
        
        # Initialize LLM client if not provided
        if llm_client is None:
            from src.core.llm_factory import LLMClientFactory
            self.llm_client = LLMClientFactory.create_client(config)
        else:
            self.llm_client = llm_client
        
        self.language = "en"
        self.analysis_history = []
    
    def analyze_modular(
        self,
        document: Document,
        mode: str = AnalysisMode.STEPWISE,
        tasks: List[str] = None,
        language: str = "en"
    ) -> ModularAnalysisResult:
        """Analyze a document using the modular approach with the specified mode."""
        # Convert mode string to enum if needed
        if isinstance(mode, str):
            mode = getattr(AnalysisMode, mode.upper(), AnalysisMode.STEPWISE)
        
        # Use all tasks if not specified
        if tasks is None:
            tasks = [t for t in TaskType]  # All available tasks
        else:
            # Convert string task names to enum
            tasks = [getattr(TaskType, t.upper()) if isinstance(t, str) else t for t in tasks]
        
        # Check cache first if enabled
        if self.use_cache and self.cache_manager:
            task_strings = [t.name.lower() if hasattr(t, 'name') else str(t).lower() for t in tasks]
            cached_result = self.cache_manager.get_cached_result(
                doc_id=document.doc_id, 
                text=document.text, 
                mode=mode.name.lower() if hasattr(mode, 'name') else str(mode).lower(),
                tasks=task_strings
            )
            
            if cached_result:
                self.logger.info(f"Using cached result for document {document.doc_id}")
                return ModularAnalysisResult.from_dict(cached_result)
        
        start_time = time.time()
        context = AnalysisContext(
            doc_id=document.doc_id,
            title=document.title,
            text=document.text,
            language=language
        )
        
        result = ModularAnalysisResult(
            doc_id=document.doc_id,
            title=document.title,
            mode=mode,
            tasks=tasks,
            language=language
        )
        
        if mode == AnalysisMode.UNIFIED:
            self._analyze_unified(context, tasks, result)
        elif mode == AnalysisMode.STEPWISE:
            self._analyze_stepwise(context, tasks, result)
        else:
            self._analyze_selective(context, tasks, result)
        
        result.execution_time = time.time() - start_time
        result.quality_score = self._calc_quality(result)
        result.warnings = self._gen_warnings(result)
        
        # Cache result if quality is acceptable and caching is enabled
        if self.use_cache and self.cache_manager and result.quality_score >= 0.3:
            self.logger.info(f"Caching result for document {result.doc_id}")
            task_strings = [t.name.lower() if hasattr(t, 'name') else str(t).lower() for t in tasks]
            self.cache_manager.save_to_cache(
                doc_id=document.doc_id, 
                text=document.text, 
                mode=mode.name.lower() if hasattr(mode, 'name') else str(mode).lower(),
                tasks=task_strings,
                result=result.to_dict()
            )
        
        self.analysis_history.append(result)
        return result
    
    def _clean_json_response(self, response: str) -> str:
        """Clean LLM response to extract valid JSON.
        
        Args:
            response: Raw LLM response
            
        Returns:
            Cleaned JSON string
        """
        response = response.strip()
        
        # Remove markdown code blocks if present
        if "```json" in response:
            response = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:
            response = response.split("```")[1].split("```")[0].strip()
        
        # Try to find JSON object boundaries
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        
        if json_start >= 0 and json_end > json_start:
            response = response[json_start:json_end]
        
        return response
    
    def _analyze_unified(self, context, tasks, result):
        """Run unified analysis."""
        prompt = self.modular_prompts.get_unified_prompt(context, tasks)
        response = self.llm_client.generate_response(prompt)
        
        try:
            # Clean the response before parsing
            cleaned_response = self._clean_json_response(response)
            parsed = json.loads(cleaned_response)
            result.analysis = parsed
            
            # Extract task results
            for task in tasks:
                if task == TaskType.ACTORS_RELATIONSHIPS:
                    result.task_results[task] = {
                        "actors": parsed.get("actors", []),
                        "relationships": parsed.get("relationships", {})
                    }
                elif task == TaskType.PORTRAYALS:
                    result.task_results[task] = {"portrayals": parsed.get("portrayals", {})}
                elif task == TaskType.ISSUE_SCOPE:
                    result.task_results[task] = {"issue_scope": parsed.get("issue_scope", {})}
                elif task == TaskType.CAUSAL_MECHANISMS:
                    result.task_results[task] = {"causal_mechanisms": parsed.get("causal_mechanisms", {})}
        except Exception as e:
            logger.error(f"Parse error: {e}")
            result.warnings.append(str(e))
    
    def _analyze_stepwise(self, context, tasks, result):
        """Run stepwise analysis."""
        for task in self._sort_tasks(tasks):
            try:
                # Update context with previous results
                if TaskType.ACTORS_RELATIONSHIPS in result.task_results:
                    actors = result.task_results[TaskType.ACTORS_RELATIONSHIPS].get("actors", [])
                    context.known_actors = [a["name"] for a in actors]
                
                prompt = self.modular_prompts.get_prompt(task, context)
                start = time.time()
                response = self.llm_client.generate_response(prompt)
                
                # Clean the response before parsing
                cleaned_response = self._clean_json_response(response)
                parsed = json.loads(cleaned_response)
                result.task_results[task] = parsed
                result.task_timings[task] = time.time() - start
                
                context.previous_results[task] = parsed
            except Exception as e:
                logger.error(f"Task {task.value} error: {e}")
                result.task_results[task] = {"error": str(e)}
        
        # Aggregate results
        result.analysis = self._aggregate(result.task_results)
    
    def _analyze_selective(self, context, tasks, result):
        """Run selective analysis."""
        # Add actor task if needed by dependencies
        if TaskType.ACTORS_RELATIONSHIPS not in tasks:
            for task in tasks:
                deps = self.modular_prompts.get_task_dependencies(task)
                if TaskType.ACTORS_RELATIONSHIPS in deps:
                    tasks.insert(0, TaskType.ACTORS_RELATIONSHIPS)
                    break
        
        self._analyze_stepwise(context, tasks, result)
    
    def _sort_tasks(self, tasks):
        """Sort tasks by dependencies."""
        sorted_tasks = []
        remaining = list(tasks)
        
        while remaining:
            for task in remaining[:]:
                deps = self.modular_prompts.get_task_dependencies(task)
                if all(d in sorted_tasks or d not in tasks for d in deps):
                    sorted_tasks.append(task)
                    remaining.remove(task)
            
            if len(sorted_tasks) == len(tasks):
                break
        
        return sorted_tasks
    
    def _aggregate(self, task_results):
        """Aggregate task results."""
        agg = {}
        
        for task, result in task_results.items():
            if "error" not in result:
                if task == TaskType.ACTORS_RELATIONSHIPS:
                    agg["actors"] = result.get("actors", [])
                    agg["relationships"] = result.get("relationships", {})
                elif task == TaskType.PORTRAYALS:
                    agg["portrayals"] = result.get("portrayals", {})
                elif task == TaskType.ISSUE_SCOPE:
                    agg["issue_scope"] = result.get("issue_scope", {})
                elif task == TaskType.CAUSAL_MECHANISMS:
                    agg["causal_mechanisms"] = result.get("causal_mechanisms", {})
        
        # Collect AI decisions
        ai_decisions = []
        for result in task_results.values():
            if "ai_decisions" in result:
                ai_decisions.extend(result["ai_decisions"])
        
        if ai_decisions:
            agg["ai_decisions"] = ai_decisions
        
        return agg
    
    def _calc_quality(self, result):
        """Calculate quality score."""
        scores = []
        
        for task, task_result in result.task_results.items():
            if "error" in task_result:
                scores.append(0.0)
            else:
                score = 0.5  # Base score
                
                if task == TaskType.ACTORS_RELATIONSHIPS:
                    actors = task_result.get("actors", [])
                    score = min(1.0, len(actors) * 0.2) if actors else 0.0
                elif task == TaskType.PORTRAYALS:
                    items = task_result.get("portrayals", {}).get("items", [])
                    score = min(1.0, len(items) * 0.25) if items else 0.2
                elif task == TaskType.ISSUE_SCOPE:
                    items = task_result.get("issue_scope", {}).get("items", [])
                    score = min(1.0, len(items) * 0.25) if items else 0.2
                elif task == TaskType.CAUSAL_MECHANISMS:
                    items = task_result.get("causal_mechanisms", {}).get("items", [])
                    score = min(1.0, len(items) * 0.25) if items else 0.2
                
                scores.append(score)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def _gen_warnings(self, result):
        """Generate warnings."""
        warnings = []
        
        for task, task_result in result.task_results.items():
            if "error" in task_result:
                warnings.append(f"Task {task.value} failed: {task_result['error']}")
            
            # Check for skipped sections
            for key in ["relationships", "portrayals", "issue_scope", "causal_mechanisms"]:
                if key in task_result:
                    data = task_result[key]
                    if isinstance(data, dict) and data.get("skipped"):
                        warnings.append(f"{key} skipped: {data.get('skip_reason', 'Unknown')}")
        
        # Quality score thresholds with more informative warnings
        if result.quality_score < 0.3:
            warnings.append("Critical quality issue: Score below 0.3 - Review required")
        elif result.quality_score < 0.5:
            warnings.append("Low quality score: Between 0.3-0.5 - May need improvement")
        elif result.quality_score < 0.7:
            warnings.append("Moderate quality: Between 0.5-0.7 - Consider refinements")
        
        return warnings
