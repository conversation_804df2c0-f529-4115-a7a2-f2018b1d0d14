#!/usr/bin/env python3
"""
Debug script to test imports and identify issues.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    import json
    import time
    print("   ✓ Basic imports OK")
    
    print("2. Testing config import...")
    from src.core.config import Config
    print("   ✓ Config import OK")
    
    print("3. Testing LLM client import...")
    from src.core.llm_client import MockLLMClient, create_llm_client
    print("   ✓ LLM client import OK")
    
    print("4. Testing analyzer import...")
    from src.core.analyzer import DocumentAnalyzer
    print("   ✓ Analyzer import OK")
    
    print("5. Testing document reader import...")
    from src.utils.document_reader import create_sample_document
    print("   ✓ Document reader import OK")
    
    print("6. Creating config...")
    config = Config()
    config.use_mock_llm = True
    print("   ✓ Config creation OK")
    
    print("7. Creating analyzer...")
    analyzer = DocumentAnalyzer(config)
    print("   ✓ Analyzer creation OK")
    
    print("8. Creating sample document...")
    document = create_sample_document()
    print("   ✓ Sample document creation OK")
    
    print("9. Testing analysis...")
    result = analyzer.analyze_document(document, "unified_analysis")
    print(f"   ✓ Analysis OK - Quality: {result.quality_score:.2f}")
    
    print("\nAll tests passed!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
