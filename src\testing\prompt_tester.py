"""
Prompt testing framework for evaluating different prompt templates.
"""

import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

from ..core.prompts import PromptTemplates, PromptTemplate
from ..core.config import Config, LLMConfig
from ..core.llm_client import create_llm_client
from ..utils.document_reader import Document, create_sample_document

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """Result of a prompt test."""
    test_id: str
    template_name: str
    document_id: str
    prompt_text: str
    response_text: str
    parsed_result: Optional[Dict[str, Any]] = None
    parse_success: bool = False
    execution_time: float = 0.0
    token_count: int = 0
    error_message: Optional[str] = None
    quality_score: float = 0.0
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

@dataclass
class TestMetrics:
    """Metrics for prompt testing."""
    total_tests: int = 0
    successful_tests: int = 0
    failed_tests: int = 0
    average_execution_time: float = 0.0
    average_quality_score: float = 0.0
    parse_success_rate: float = 0.0
    template_performance: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.template_performance is None:
            self.template_performance = {}

class PromptTester:
    """Framework for testing prompt templates."""
    
    def __init__(self, config: Config):
        self.config = config
        self.prompt_templates = PromptTemplates()
        self.test_results: List[TestResult] = []
        self.metrics: TestMetrics = TestMetrics()
        
        # Create directories
        self.output_dir = Path(config.output_dir) / "prompt_tests"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup LLM client
        self.llm_client = self._setup_llm_client()
    
    def _setup_llm_client(self):
        """Setup LLM client based on configuration."""
        return create_llm_client(self.config.llm, self.config.use_mock_llm)
    
    def test_template(self, template_name: str, document: Document, 
                     variables: Optional[Dict[str, str]] = None) -> TestResult:
        """Test a single template with a document."""
        
        if variables is None:
            variables = {
                'DOC_ID': document.doc_id,
                'TITLE': document.title or '',
                'DOCUMENT_TEXT': document.text,
                'KNOWN_ACTORS': ''
            }
        
        template = self.prompt_templates.get_template(template_name)
        prompt_text = self.prompt_templates.format_template(template_name, variables)
        
        test_id = f"{template_name}_{document.doc_id}_{int(time.time())}"
        
        logger.info(f"Testing template '{template_name}' with document '{document.doc_id}'")
        
        start_time = time.time()
        
        try:
            # Get LLM response
            response = self.llm_client.generate_response(prompt_text)
            execution_time = time.time() - start_time
            
            # Parse response
            parsed_result = None
            parse_success = False
            quality_score = 0.0
            
            try:
                parsed_result = json.loads(response)
                parse_success = True
                
                # Calculate quality score
                quality_score = self._calculate_quality_score(parsed_result)
                
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
            
            # Count tokens (approximate)
            token_count = len(prompt_text.split()) + len(response.split())
            
            result = TestResult(
                test_id=test_id,
                template_name=template_name,
                document_id=document.doc_id,
                prompt_text=prompt_text,
                response_text=response,
                parsed_result=parsed_result,
                parse_success=parse_success,
                execution_time=execution_time,
                token_count=token_count,
                quality_score=quality_score
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error testing template '{template_name}': {e}")
            
            result = TestResult(
                test_id=test_id,
                template_name=template_name,
                document_id=document.doc_id,
                prompt_text=prompt_text,
                response_text="",
                execution_time=execution_time,
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    def test_all_templates(self, document: Document, 
                         template_names: Optional[List[str]] = None) -> List[TestResult]:
        """Test all templates with a document."""
        
        if template_names is None:
            template_names = self.prompt_templates.get_template_names()
        
        results = []
        for template_name in template_names:
            try:
                result = self.test_template(template_name, document)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to test template '{template_name}': {e}")
                continue
        
        return results
    
    def test_with_multiple_documents(self, documents: List[Document],
                                   template_names: Optional[List[str]] = None) -> List[TestResult]:
        """Test templates with multiple documents."""
        
        all_results = []
        
        for document in documents:
            logger.info(f"Testing with document: {document.doc_id}")
            results = self.test_all_templates(document, template_names)
            all_results.extend(results)
        
        return all_results
    
    def calculate_metrics(self) -> TestMetrics:
        """Calculate test metrics."""
        if not self.test_results:
            return TestMetrics()
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.parse_success)
        failed_tests = total_tests - successful_tests
        
        avg_execution_time = sum(r.execution_time for r in self.test_results) / total_tests
        avg_quality_score = sum(r.quality_score for r in self.test_results if r.parse_success) / max(1, successful_tests)
        parse_success_rate = successful_tests / total_tests
        
        # Template performance
        template_performance = {}
        for result in self.test_results:
            template_name = result.template_name
            if template_name not in template_performance:
                template_performance[template_name] = {
                    'total_tests': 0,
                    'successful_tests': 0,
                    'avg_execution_time': 0,
                    'avg_quality_score': 0,
                    'parse_success_rate': 0
                }
            
            perf = template_performance[template_name]
            perf['total_tests'] += 1
            if result.parse_success:
                perf['successful_tests'] += 1
        
        # Calculate averages for each template
        for template_name, perf in template_performance.items():
            template_results = [r for r in self.test_results if r.template_name == template_name]
            successful_template_results = [r for r in template_results if r.parse_success]
            
            perf['avg_execution_time'] = sum(r.execution_time for r in template_results) / len(template_results)
            perf['avg_quality_score'] = sum(r.quality_score for r in successful_template_results) / max(1, len(successful_template_results))
            perf['parse_success_rate'] = perf['successful_tests'] / perf['total_tests']
        
        return TestMetrics(
            total_tests=total_tests,
            successful_tests=successful_tests,
            failed_tests=failed_tests,
            average_execution_time=avg_execution_time,
            average_quality_score=avg_quality_score,
            parse_success_rate=parse_success_rate,
            template_performance=template_performance
        )
    
    def _calculate_quality_score(self, parsed_result: Dict[str, Any]) -> float:
        """Calculate a quality score for parsed results."""
        score = 0.0
        max_score = 0.0
        
        # Check for actors
        if 'actors' in parsed_result:
            max_score += 0.3
            actors = parsed_result['actors']
            if isinstance(actors, list) and len(actors) > 0:
                score += min(0.3, len(actors) * 0.1)
        
        # Check for relationships
        if 'relationships' in parsed_result:
            max_score += 0.2
            rel_data = parsed_result['relationships']
            if isinstance(rel_data, dict) and 'items' in rel_data:
                items = rel_data['items']
                if isinstance(items, list) and len(items) > 0:
                    score += min(0.2, len(items) * 0.05)
        
        # Check for portrayals
        if 'portrayals' in parsed_result:
            max_score += 0.2
            port_data = parsed_result['portrayals']
            if isinstance(port_data, dict) and 'items' in port_data:
                items = port_data['items']
                if isinstance(items, list) and len(items) > 0:
                    score += min(0.2, len(items) * 0.1)
        
        # Check for issue scope
        if 'issue_scope' in parsed_result:
            max_score += 0.15
            issue_data = parsed_result['issue_scope']
            if isinstance(issue_data, dict) and 'items' in issue_data:
                items = issue_data['items']
                if isinstance(items, list) and len(items) > 0:
                    score += min(0.15, len(items) * 0.075)
        
        # Check for causal mechanisms
        if 'causal_mechanisms' in parsed_result:
            max_score += 0.15
            causal_data = parsed_result['causal_mechanisms']
            if isinstance(causal_data, dict) and 'items' in causal_data:
                items = causal_data['items']
                if isinstance(items, list) and len(items) > 0:
                    score += min(0.15, len(items) * 0.075)
        
        return score / max_score if max_score > 0 else 0.0
    
    def save_results(self, filename: Optional[str] = None) -> str:
        """Save test results to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prompt_test_results_{timestamp}.json"
        
        output_file = self.output_dir / filename
        
        results_data = {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_tests': len(self.test_results),
                'config': self.config.to_dict()
            },
            'results': [asdict(result) for result in self.test_results],
            'metrics': asdict(self.calculate_metrics())
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved test results to {output_file}")
        return str(output_file)
    
    def generate_report(self, output_dir: Optional[Path] = None) -> str:
        """Generate a comprehensive test report."""
        if output_dir is None:
            output_dir = self.output_dir / "report"
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        metrics = self.calculate_metrics()
        
        # Save detailed results
        results_file = self.save_results("detailed_results.json")
        
        # Create summary report
        summary_file = output_dir / "summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"Prompt Testing Report\\n")
            f.write(f"====================\\n\\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n")
            
            f.write(f"Overall Metrics:\\n")
            f.write(f"- Total Tests: {metrics.total_tests}\\n")
            f.write(f"- Successful Tests: {metrics.successful_tests}\\n")
            f.write(f"- Failed Tests: {metrics.failed_tests}\\n")
            f.write(f"- Success Rate: {metrics.parse_success_rate:.2%}\\n")
            f.write(f"- Average Execution Time: {metrics.average_execution_time:.2f}s\\n")
            f.write(f"- Average Quality Score: {metrics.average_quality_score:.2f}\\n\\n")
            
            f.write("Template Performance:\\n")
            f.write("-" * 50 + "\\n")
            
            for template_name, perf in metrics.template_performance.items():
                f.write(f"\\n{template_name}:\\n")
                f.write(f"  - Tests: {perf['total_tests']}\\n")
                f.write(f"  - Success Rate: {perf['parse_success_rate']:.2%}\\n")
                f.write(f"  - Avg Execution Time: {perf['avg_execution_time']:.2f}s\\n")
                f.write(f"  - Avg Quality Score: {perf['avg_quality_score']:.2f}\\n")
        
        # Generate visualizations
        self._generate_visualizations(metrics, output_dir)
        
        logger.info(f"Generated report in {output_dir}")
        return str(output_dir)
    
    def _generate_visualizations(self, metrics: TestMetrics, output_dir: Path) -> None:
        """Generate visualizations for the test report."""
        try:
            # Prepare data for visualization
            template_names = list(metrics.template_performance.keys())
            success_rates = [metrics.template_performance[name]['parse_success_rate'] for name in template_names]
            execution_times = [metrics.template_performance[name]['avg_execution_time'] for name in template_names]
            quality_scores = [metrics.template_performance[name]['avg_quality_score'] for name in template_names]
            
            # Create success rate chart
            plt.figure(figsize=(12, 6))
            plt.subplot(1, 3, 1)
            plt.bar(template_names, success_rates)
            plt.title('Parse Success Rate by Template')
            plt.xlabel('Template')
            plt.ylabel('Success Rate')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(output_dir / 'success_rates.png', dpi=300, bbox_inches='tight')
            
            # Create execution time chart
            plt.figure(figsize=(12, 6))
            plt.subplot(1, 3, 2)
            plt.bar(template_names, execution_times)
            plt.title('Average Execution Time by Template')
            plt.xlabel('Template')
            plt.ylabel('Time (seconds)')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(output_dir / 'execution_times.png', dpi=300, bbox_inches='tight')
            
            # Create quality score chart
            plt.figure(figsize=(12, 6))
            plt.subplot(1, 3, 3)
            plt.bar(template_names, quality_scores)
            plt.title('Average Quality Score by Template')
            plt.xlabel('Template')
            plt.ylabel('Quality Score')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(output_dir / 'quality_scores.png', dpi=300, bbox_inches='tight')
            
            plt.close('all')
            
        except Exception as e:
            logger.error(f"Error generating visualizations: {e}")
    
    def load_results(self, filename: str) -> None:
        """Load test results from file."""
        input_file = Path(filename)
        
        if not input_file.exists():
            raise FileNotFoundError(f"Results file not found: {filename}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Load results
        self.test_results = []
        for result_data in data.get('results', []):
            result = TestResult(**result_data)
            self.test_results.append(result)
        
        logger.info(f"Loaded {len(self.test_results)} test results from {filename}")


def main():
    """Main function for running prompt tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test prompt templates")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--document", "-d", help="Document file to test")
    parser.add_argument("--directory", "-dir", help="Directory of documents to test")
    parser.add_argument("--templates", "-t", nargs="+", help="Template names to test")
    parser.add_argument("--output", "-o", help="Output directory")
    parser.add_argument("--mock", action="store_true", help="Use mock LLM")
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        config = Config.from_file(args.config)
    else:
        config = Config()
    
    if args.mock:
        config.use_mock_llm = True
    
    if args.output:
        config.output_dir = args.output
    
    # Create tester
    tester = PromptTester(config)
    
    # Get documents
    documents = []
    if args.document:
        from ..utils.document_reader import DocumentReader
        reader = DocumentReader()
        documents.append(reader.read_file(args.document))
    elif args.directory:
        from ..utils.document_reader import DocumentReader
        reader = DocumentReader()
        documents = reader.read_directory(args.directory)
    else:
        # Use sample document
        documents = [create_sample_document()]
    
    # Run tests
    if args.templates:
        template_names = args.templates
    else:
        template_names = tester.prompt_templates.get_template_names()
    
    all_results = []
    for doc in documents:
        results = tester.test_all_templates(doc, template_names)
        all_results.extend(results)
    
    # Generate report
    report_dir = tester.generate_report()
    print(f"Test report generated in: {report_dir}")
    
    # Print summary
    metrics = tester.calculate_metrics()
    print(f"\\nSummary:")
    print(f"Total tests: {metrics.total_tests}")
    print(f"Successful tests: {metrics.successful_tests}")
    print(f"Success rate: {metrics.parse_success_rate:.2%}")
    print(f"Average quality score: {metrics.average_quality_score:.2f}")

if __name__ == "__main__":
    main()