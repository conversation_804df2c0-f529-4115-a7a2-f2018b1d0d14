"""
Test script for modular document analysis system.

This script demonstrates the new modular analysis capabilities including:
- Unified analysis (all tasks in one prompt)
- Stepwise analysis (sequential task execution)
- Selective analysis (specific tasks only)
"""

import json
import time
from pathlib import Path
from src.core.config import Config
from src.core.modular_analyzer import Modular<PERSON><PERSON>ument<PERSON>nalyzer
from src.core.modular_prompts import AnalysisMode, TaskType
from src.utils.document_reader import Document

def create_test_document():
    """Create a test policy document."""
    return Document(
        doc_id="test_001",
        title="Environmental Protection Policy Analysis",
        text="""
        The Environmental Protection Agency (EPA) has announced new regulations 
        targeting industrial polluters who have been contaminating local water supplies. 
        
        Environmental groups praise the EPA for finally taking action against 
        corporate polluters who have knowingly damaged ecosystems for profit. 
        These companies have deliberately ignored safety standards, putting 
        community health at risk.
        
        Industry representatives argue that the new regulations are overly 
        restrictive and will harm economic growth. They claim that unintended 
        consequences of past policies have already created challenges for businesses 
        trying to comply with environmental standards.
        
        Local communities affected by pollution support the stricter measures, 
        stating they have suffered from health problems caused by industrial waste. 
        The expansion of regulatory scope to include air quality monitoring 
        represents a comprehensive approach to environmental protection.
        """
    )

def test_unified_analysis(analyzer, document):
    """Test unified analysis mode."""
    print("\n" + "="*60)
    print("TESTING UNIFIED ANALYSIS MODE")
    print("="*60)
    
    result = analyzer.analyze_modular(
        document,
        mode=AnalysisMode.UNIFIED,
        tasks=None  # All tasks
    )
    
    print(f"\nExecution Time: {result.execution_time:.2f} seconds")
    print(f"Quality Score: {result.quality_score:.2f}")
    print(f"Warnings: {len(result.warnings)}")
    
    if result.analysis:
        print("\nAnalysis Results:")
        print(json.dumps(result.analysis, indent=2, ensure_ascii=False)[:500] + "...")
    
    return result

def test_stepwise_analysis(analyzer, document):
    """Test stepwise analysis mode."""
    print("\n" + "="*60)
    print("TESTING STEPWISE ANALYSIS MODE")
    print("="*60)
    
    result = analyzer.analyze_modular(
        document,
        mode=AnalysisMode.STEPWISE,
        tasks=None  # All tasks
    )
    
    print(f"\nExecution Time: {result.execution_time:.2f} seconds")
    print(f"Quality Score: {result.quality_score:.2f}")
    
    print("\nTask Timings:")
    for task, timing in result.task_timings.items():
        print(f"  {task.value}: {timing:.2f} seconds")
    
    print("\nTask Results Summary:")
    for task, task_result in result.task_results.items():
        if "error" in task_result:
            print(f"  {task.value}: ERROR - {task_result['error']}")
        else:
            print(f"  {task.value}: SUCCESS")
            if task == TaskType.ACTORS_RELATIONSHIPS:
                actors = task_result.get("actors", [])
                print(f"    - Found {len(actors)} actors")
    
    return result

def test_selective_analysis(analyzer, document):
    """Test selective analysis mode."""
    print("\n" + "="*60)
    print("TESTING SELECTIVE ANALYSIS MODE")
    print("="*60)
    print("(Only analyzing actors and portrayals)")
    
    result = analyzer.analyze_modular(
        document,
        mode=AnalysisMode.SELECTIVE,
        tasks=[TaskType.ACTORS_RELATIONSHIPS, TaskType.PORTRAYALS]
    )
    
    print(f"\nExecution Time: {result.execution_time:.2f} seconds")
    print(f"Quality Score: {result.quality_score:.2f}")
    
    print("\nTasks Executed:")
    for task in result.task_results.keys():
        print(f"  - {task.value}")
    
    return result

def test_bilingual_analysis(analyzer, document):
    """Test analysis in different languages."""
    print("\n" + "="*60)
    print("TESTING BILINGUAL SUPPORT")
    print("="*60)
    
    # Test English
    print("\n1. English Analysis:")
    analyzer.language = "en"
    en_result = analyzer.analyze_modular(
        document,
        mode=AnalysisMode.STEPWISE,
        tasks=[TaskType.ACTORS_RELATIONSHIPS]
    )
    print(f"   Execution Time: {en_result.execution_time:.2f} seconds")
    
    # Test Chinese
    print("\n2. Chinese Analysis:")
    analyzer.language = "zh"
    zh_result = analyzer.analyze_modular(
        document,
        mode=AnalysisMode.STEPWISE,
        tasks=[TaskType.ACTORS_RELATIONSHIPS]
    )
    print(f"   Execution Time: {zh_result.execution_time:.2f} seconds")
    
    return en_result, zh_result

def save_results(results, output_dir="test_results"):
    """Save test results to files."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    for name, result in results.items():
        filename = output_path / f"{name}_{timestamp}.json"
        
        # Convert result to dictionary
        result_dict = {
            "doc_id": result.doc_id,
            "title": result.title,
            "mode": result.mode.value,
            "analysis": result.analysis,
            "task_results": {k.value: v for k, v in result.task_results.items()},
            "task_timings": {k.value: v for k, v in result.task_timings.items()},
            "quality_score": result.quality_score,
            "execution_time": result.execution_time,
            "warnings": result.warnings,
            "metadata": result.metadata
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {name} results to {filename}")

def main():
    """Main test function."""
    print("="*60)
    print("MODULAR DOCUMENT ANALYSIS SYSTEM TEST")
    print("="*60)
    
    # Initialize configuration
    print("\nInitializing configuration...")
    config = Config()
    
    # Check if using mock LLM
    if config.use_mock_llm:
        print("WARNING: Using mock LLM for testing")
        print("Set API keys in .env file to use real LLM")
    else:
        print(f"Using {config.llm.provider} LLM ({config.llm.model})")
    
    # Create analyzer
    print("\nCreating modular analyzer...")
    analyzer = ModularDocumentAnalyzer(config)
    
    # Create test document
    print("\nCreating test document...")
    document = create_test_document()
    print(f"Document ID: {document.doc_id}")
    print(f"Document Title: {document.title}")
    print(f"Document Length: {len(document.text)} characters")
    
    # Run tests
    results = {}
    
    try:
        # Test 1: Unified Analysis
        results["unified"] = test_unified_analysis(analyzer, document)
        
        # Test 2: Stepwise Analysis
        results["stepwise"] = test_stepwise_analysis(analyzer, document)
        
        # Test 3: Selective Analysis
        results["selective"] = test_selective_analysis(analyzer, document)
        
        # Test 4: Bilingual Support (if not using mock)
        if not config.use_mock_llm:
            en_result, zh_result = test_bilingual_analysis(analyzer, document)
            results["bilingual_en"] = en_result
            results["bilingual_zh"] = zh_result
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        import traceback
        traceback.print_exc()
    
    # Save results
    if results:
        print("\n" + "="*60)
        print("SAVING TEST RESULTS")
        print("="*60)
        save_results(results)
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Tests Completed: {len(results)}")
    
    for name, result in results.items():
        print(f"\n{name}:")
        print(f"  - Quality Score: {result.quality_score:.2f}")
        print(f"  - Execution Time: {result.execution_time:.2f}s")
        print(f"  - Warnings: {len(result.warnings)}")
    
    print("\n" + "="*60)
    print("TEST COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
