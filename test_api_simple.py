#!/usr/bin/env python3
"""
Simple API test script.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8006"

def test_health():
    """Test health endpoint."""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"Health check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_modes():
    """Test modes endpoint."""
    print("Testing modes endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/analyze/modes", timeout=5)
        print(f"Modes check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Available modes: {len(data.get('modes', []))}")
            print(f"Available tasks: {len(data.get('tasks', []))}")
        return response.status_code == 200
    except Exception as e:
        print(f"Modes check failed: {e}")
        return False

def test_modular_analysis():
    """Test modular analysis endpoint."""
    print("Testing modular analysis endpoint...")
    try:
        payload = {
            "doc_id": "test_001",
            "title": "Test Document",
            "text": "The Environmental Protection Agency announced new regulations. Environmental groups praised the decision.",
            "mode": "unified",
            "language": "en"
        }
        
        response = requests.post(f"{BASE_URL}/analyze/modular", 
                               json=payload, 
                               timeout=30)
        print(f"Modular analysis: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Analysis completed - Quality: {data.get('quality_score', 'N/A')}")
        else:
            print(f"Error response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Modular analysis failed: {e}")
        return False

if __name__ == "__main__":
    print("Starting API tests...")
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    tests = [
        ("Health Check", test_health),
        ("Modes Check", test_modes),
        ("Modular Analysis", test_modular_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        print(f"Result: {'PASS' if result else 'FAIL'}")
    
    print(f"\n--- Summary ---")
    for test_name, result in results:
        print(f"{test_name}: {'PASS' if result else 'FAIL'}")
    
    passed = sum(1 for _, result in results if result)
    print(f"\nPassed: {passed}/{len(results)} tests")
