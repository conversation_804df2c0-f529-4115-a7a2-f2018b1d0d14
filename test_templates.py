#!/usr/bin/env python3
"""
测试不同模板的实际效果
"""

import os
import sys
import json
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import Config
from core.analyzer import DocumentAnalyzer
from utils.document_reader import create_sample_document

def test_template_performance():
    """测试不同模板的性能和效果"""
    
    print("=== 测试不同模板的实际效果 ===\n")
    
    # 创建配置
    config = Config()
    
    # 创建分析器
    analyzer = DocumentAnalyzer(config)
    
    # 创建示例文档
    document = create_sample_document()
    
    print(f"测试文档: {document.title}")
    print(f"文档长度: {len(document.text)} 字符")
    print(f"文档内容预览: {document.text[:200]}...\n")
    
    # 要测试的模板列表
    templates_to_test = [
        "unified_analysis",
        "actor_extraction", 
        "relationship_analysis",
        "portrayal_analysis",
        "issue_scope_analysis",
        "causal_mechanism_analysis",
        "openai_optimized",
        "anthropic_optimized",
        "zhipu_optimized"
    ]
    
    results = {}
    
    for template_name in templates_to_test:
        print(f"--- 测试模板: {template_name} ---")
        
        try:
            start_time = time.time()
            
            # 使用指定模板分析文档
            result = analyzer.analyze_document(document, template_name)
            
            end_time = time.time()
            
            # 记录结果
            results[template_name] = {
                'success': True,
                'execution_time': end_time - start_time,
                'quality_score': result.quality_score,
                'warnings': result.warnings,
                'analysis_keys': list(result.analysis.keys()) if result.analysis else [],
                'actors_count': len(result.analysis.get('actors', [])) if result.analysis else 0,
                'relationships_count': len(result.analysis.get('relationships', {}).get('items', [])) if result.analysis else 0,
                'portrayals_count': len(result.analysis.get('portrayals', {}).get('items', [])) if result.analysis else 0,
                'issue_scope_count': len(result.analysis.get('issue_scope', {}).get('items', [])) if result.analysis else 0,
                'causal_mechanisms_count': len(result.analysis.get('causal_mechanisms', {}).get('items', [])) if result.analysis else 0
            }
            
            print(f"成功")
            print(f"   执行时间: {results[template_name]['execution_time']:.2f}秒")
            print(f"   质量分数: {results[template_name]['quality_score']:.3f}")
            print(f"   分析组件: {results[template_name]['analysis_keys']}")
            print(f"   警告: {len(results[template_name]['warnings'])}")
            
            if result.warnings:
                for warning in result.warnings:
                    print(f"     - {warning}")
            
            # 显示分析结果的关键指标
            analysis = result.analysis
            if analysis:
                print(f"   行动者数量: {len(analysis.get('actors', []))}")
                print(f"   关系数量: {len(analysis.get('relationships', {}).get('items', []))}")
                print(f"   角色塑造数量: {len(analysis.get('portrayals', {}).get('items', []))}")
                print(f"   问题范围数量: {len(analysis.get('issue_scope', {}).get('items', []))}")
                print(f"   因果机制数量: {len(analysis.get('causal_mechanisms', {}).get('items', []))}")
            
            print()
            
        except Exception as e:
            results[template_name] = {
                'success': False,
                'error': str(e),
                'execution_time': 0,
                'quality_score': 0
            }
            
            print(f"失败: {e}\n")
    
    # 生成比较报告
    print("=== 模板性能比较报告 ===\n")
    
    successful_results = {k: v for k, v in results.items() if v['success']}
    
    if successful_results:
        print(f"成功测试的模板数量: {len(successful_results)}/{len(templates_to_test)}")
        print()
        
        # 按执行时间排序
        print("执行时间排名（从快到慢）:")
        sorted_by_time = sorted(successful_results.items(), key=lambda x: x[1]['execution_time'])
        for i, (template, data) in enumerate(sorted_by_time, 1):
            print(f"{i:2d}. {template}: {data['execution_time']:.2f}秒")
        print()
        
        # 按质量分数排序
        print("质量分数排名（从高到低）:")
        sorted_by_quality = sorted(successful_results.items(), key=lambda x: x[1]['quality_score'], reverse=True)
        for i, (template, data) in enumerate(sorted_by_quality, 1):
            print(f"{i:2d}. {template}: {data['quality_score']:.3f}")
        print()
        
        # 分析组件覆盖情况
        print("分析组件覆盖情况:")
        for template, data in successful_results.items():
            components = data['analysis_keys']
            print(f"   {template}: {', '.join(components) if components else '无'}")
        print()
        
        # 具体指标比较
        print("具体指标比较:")
        headers = ["模板", "行动者", "关系", "角色", "问题", "因果", "警告"]
        print(f"{'模板':<20} {'行动者':<8} {'关系':<8} {'角色':<8} {'问题':<8} {'因果':<8} {'警告':<8}")
        print("-" * 80)
        
        for template, data in successful_results.items():
            print(f"{template:<20} {data['actors_count']:<8} {data['relationships_count']:<8} {data['portrayals_count']:<8} {data['issue_scope_count']:<8} {data['causal_mechanisms_count']:<8} {len(data['warnings']):<8}")
        print()
        
        # 模板特点分析
        print("模板特点分析:")
        template_characteristics = {
            "unified_analysis": "综合分析，包含所有组件",
            "actor_extraction": "专注于行动者提取",
            "relationship_analysis": "专注于关系分析",
            "portrayal_analysis": "专注于角色塑造分析",
            "issue_scope_analysis": "专注于问题范围策略",
            "causal_mechanism_analysis": "专注于因果机制分析",
            "openai_optimized": "OpenAI优化版本",
            "anthropic_optimized": "Anthropic优化版本",
            "zhipu_optimized": "智谱AI优化版本"
        }
        
        for template, characteristic in template_characteristics.items():
            if template in successful_results:
                data = successful_results[template]
                print(f"   {template}: {characteristic}")
                print(f"     - 执行时间: {data['execution_time']:.2f}秒")
                print(f"     - 质量分数: {data['quality_score']:.3f}")
                print(f"     - 主要组件: {', '.join(data['analysis_keys'][:3])}")
                print()
        
        # 保存结果到文件
        output_file = "template_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"测试结果已保存到: {output_file}")
        
    else:
        print("所有模板测试都失败了")
    
    return results

if __name__ == "__main__":
    test_template_performance()