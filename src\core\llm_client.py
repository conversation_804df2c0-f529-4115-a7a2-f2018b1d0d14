"""
LLM client implementations for different providers.
"""

from abc import ABC, abstractmethod
from typing import Optional
from .config import LLMConfig


class BaseLLMClient(ABC):
    """Abstract base class for LLM clients."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
    
    @abstractmethod
    def generate_response(self, prompt: str) -> str:
        """Generate response from LLM."""
        pass


class MockLLMClient(BaseLLMClient):
    """Mock LLM client for testing without API calls."""
    
    def __init__(self, config):
        super().__init__(config)
        self.call_count = 0
    
    def generate_response(self, prompt: str) -> str:
        """Generate a mock response."""
        import json
        import time
        import random
        
        # Simulate processing time
        time.sleep(0.1)
        
        # Extract document info from prompt for better mocking
        doc_id = "mock_doc"
        title = "Mock Document"
        
        # Try to extract document info from prompt
        if "DOC_ID:" in prompt:
            doc_id_start = prompt.find("DOC_ID:") + 7
            doc_id_end = prompt.find("\n", doc_id_start)
            if doc_id_end != -1:
                doc_id = prompt[doc_id_start:doc_id_end].strip()
        
        if "title:" in prompt:
            title_start = prompt.find("title:") + 6
            title_end = prompt.find("\n", title_start)
            if title_end != -1:
                title = prompt[title_start:title_end].strip()
        
        # Detect template type from prompt content
        template_type = "unified_analysis"  # default
        if "quick summary" in prompt.lower():
            template_type = "quick_summary"
        elif "actor_extraction" in prompt:
            template_type = "actor_extraction"
        
        self.call_count += 1
        
        # Generate responses based on template type
        if template_type == "quick_summary":
            # Quick summary response
            return json.dumps({
                "doc_id": doc_id,
                "title": title,
                "summary": {
                    "main_topic": "AI regulation and policy development",
                    "key_actors": [
                        {
                            "name": "U.S. Department of Commerce",
                            "stakeholder_category": "Government",
                            "role": "Leading AI policy implementation"
                        },
                        {
                            "name": "President Biden",
                            "stakeholder_category": "Government",
                            "role": "Executive order initiator"
                        }
                    ],
                    "key_points": [
                        "New guidance and tools for AI governance",
                        "270 days following executive order",
                        "Focus on responsible AI development"
                    ],
                    "document_type": "announcement"
                }
            })
        
        # Generate varied mock responses based on call count for unified analysis
        if self.call_count % 3 == 0:
            # High quality response
            return json.dumps({
                "doc_id": doc_id,
                "title": title,
                "actors": [
                    {"name": "FTC", "stakeholder_category": "Government", "notes": "Federal Trade Commission"},
                    {"name": "Google", "stakeholder_category": "Corporate", "notes": "Technology company"},
                    {"name": "Consumer Advocacy Group", "stakeholder_category": "NGOs and Professional Organizations", "notes": "Consumer protection organization"}
                ],
                "relationships": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [
                        {
                            "a_name": "FTC",
                            "b_name": "Google",
                            "relationship": "regulates",
                            "a_character": "hero",
                            "b_character": "villain",
                            "a_type": "Government",
                            "b_type": "Corporate",
                            "evidence": "FTC announced new regulations targeting Big Tech companies like Google"
                        }
                    ]
                },
                "portrayals": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [
                        {
                            "actor": "FTC",
                            "type": "Hero",
                            "evidence": "FTC announced new regulations targeting Big Tech companies",
                            "explanation": "FTC is portrayed as taking action to protect consumers"
                        }
                    ],
                    "shifts": {
                        "angel_shift_present": True,
                        "devil_shift_present": False,
                        "angel_shift_evidence": "FTC is positioned as protecting consumers",
                        "devil_shift_evidence": None
                    }
                },
                "issue_scope": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [
                        {
                            "actor": "FTC",
                            "type": "Issue expansion",
                            "evidence": "regulations targeting Big Tech companies",
                            "explanation": "Broadening the scope to entire tech industry"
                        }
                    ]
                },
                "causal_mechanisms": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [
                        {
                            "actor": "Google",
                            "type": "Intentional",
                            "evidence": "engaging in anti-competitive practices",
                            "explanation": "Google is intentionally blamed for harming competition"
                        }
                    ]
                },
                "ai_decisions": []
            })
        elif self.call_count % 3 == 1:
            # Medium quality response
            return json.dumps({
                "doc_id": doc_id,
                "title": title,
                "actors": [
                    {"name": "FTC", "stakeholder_category": "Government", "notes": None},
                    {"name": "Google", "stakeholder_category": "Corporate", "notes": None}
                ],
                "relationships": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [
                        {
                            "a_name": "FTC",
                            "b_name": "Google",
                            "relationship": "regulates",
                            "a_character": None,
                            "b_character": None,
                            "a_type": "Government",
                            "b_type": "Corporate",
                            "evidence": "FTC regulates Google"
                        }
                    ]
                },
                "portrayals": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [],
                    "shifts": {
                        "angel_shift_present": False,
                        "devil_shift_present": False,
                        "angel_shift_evidence": None,
                        "devil_shift_evidence": None
                    }
                },
                "issue_scope": {
                    "skipped": True,
                    "skip_reason": "Not enough information",
                    "items": []
                },
                "causal_mechanisms": {
                    "skipped": True,
                    "skip_reason": "Not enough information",
                    "items": []
                },
                "ai_decisions": []
            })
        else:
            # Low quality response (original)
            return json.dumps({
                "doc_id": doc_id,
                "title": title,
                "actors": [
                    {"name": "Mock Actor", "stakeholder_category": "Corporate", "notes": "Mock actor for testing"}
                ],
                "relationships": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": []
                },
                "portrayals": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": [],
                    "shifts": {
                        "angel_shift_present": False,
                        "devil_shift_present": False,
                        "angel_shift_evidence": None,
                        "devil_shift_evidence": None
                    }
                },
                "issue_scope": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": []
                },
                "causal_mechanisms": {
                    "skipped": False,
                    "skip_reason": None,
                    "items": []
                },
                "ai_decisions": []
            })


class OpenAIClient(BaseLLMClient):
    """OpenAI API client."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        try:
            import openai
            self.client = openai.OpenAI(api_key=config.api_key)
        except ImportError:
            raise ImportError("OpenAI library not installed. Install with: pip install openai")
    
    def generate_response(self, prompt: str) -> str:
        """Generate response using OpenAI API."""
        try:
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                timeout=self.config.timeout
            )
            return response.choices[0].message.content
        except Exception as e:
            raise RuntimeError(f"OpenAI API error: {e}")


class AnthropicClient(BaseLLMClient):
    """Anthropic API client."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        try:
            import anthropic
            self.client = anthropic.Anthropic(api_key=config.api_key)
        except ImportError:
            raise ImportError("Anthropic library not installed. Install with: pip install anthropic")
    
    def generate_response(self, prompt: str) -> str:
        """Generate response using Anthropic API."""
        try:
            response = self.client.messages.create(
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            raise RuntimeError(f"Anthropic API error: {e}")


class ZhipuAIClient(BaseLLMClient):
    """ZhipuAI API client."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        # Set default base URL if not provided
        if not config.base_url:
            self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        else:
            self.base_url = config.base_url
        
        try:
            import requests
            self.requests = requests
        except ImportError:
            raise ImportError("Requests library not installed. Install with: pip install requests")
    
    def generate_response(self, prompt: str) -> str:
        """Generate response using ZhipuAI API."""
        try:
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "stream": False
            }
            
            response = self.requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=self.config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            raise RuntimeError(f"ZhipuAI API error: {e}")


def create_llm_client(config: LLMConfig, use_mock: bool = False) -> BaseLLMClient:
    """Create LLM client based on configuration."""
    if use_mock:
        return MockLLMClient(config)
    
    if config.provider == "openai":
        return OpenAIClient(config)
    elif config.provider == "anthropic":
        return AnthropicClient(config)
    elif config.provider == "zhipuai":
        return ZhipuAIClient(config)
    else:
        raise ValueError(f"Unsupported LLM provider: {config.provider}")