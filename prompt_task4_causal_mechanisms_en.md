# Task 4: Causal Mechanism Detection

**System Instruction:** You are a narrative analysis model. Read the input document and the provided definitions. Your task is to identify causal mechanisms. Return ONLY a valid JSON object matching the Output Schema. Do not include explanations.

**Referenced Definitions:** Please adhere to all rules in `prompt_core_definitions_en.md`.

## Task-Specific Goal

-   Detect if a problem is attributed to `Intentional` blame or `Inadvertent` consequences.
-   Provide direct evidence and a brief explanation for each instance.

## Output Schema

```json
{
  "doc_id": "string",
  "causal_mechanisms": {
    "skipped": false,
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Intentional|Inadvertent",
        "evidence": "quote or short passage (can span multiple sentences)",
        "explanation": "1-2 sentences on blame assignment or unintended consequences"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}
```

## Detailed Instructions

### Identifying Intentional Causal Mechanisms
**Definition**: Deliberate blame assignment to damage reputation or shift responsibility

**Key Indicators:**
- Direct blame language ("X caused", "X is responsible for")
- Moral responsibility attribution ("X deliberately", "X recklessly")
- Intent attribution ("X knew but ignored", "X chose to")
- Accountability language ("X must answer for", "X should be held responsible")

**Examples of Intentional Attribution:**
- "Facebook's deliberate algorithmic choices created the misinformation crisis"
- "The regulator's negligence directly caused market failure"
- "Their reckless pursuit of profit endangered users"

### Identifying Inadvertent Causal Mechanisms
**Definition**: Problems attributed to unintended consequences without moral blame

**Key Indicators:**
- Unintended consequence language ("inadvertently", "unexpectedly")
- Good intentions gone wrong ("despite best efforts", "well-meaning but")
- System/structural attribution ("the policy led to", "resulted in")
- No moral condemnation (factual cause-effect without blame)

**Examples of Inadvertent Attribution:**
- "GDPR inadvertently strengthened big tech's market position"
- "The policy had the unintended effect of reducing innovation"
- "Despite good intentions, the regulation created barriers for startups"

## Examples

### Example Input
```text
doc_id: "doc_003"
title: "Tech Regulation Impact Analysis"
text: "Critics argue that Meta deliberately ignored safety warnings to maximize engagement and profits, directly causing teen mental health issues. The company's reckless disregard for user wellbeing is unconscionable. Meanwhile, EU regulators acknowledge that GDPR, despite its privacy goals, inadvertently created compliance costs that disproportionately burden small companies, leading to unexpected market consolidation."
optional_known_actors: ["Meta", "EU"]
```

### Example Output
```json
{
  "doc_id": "doc_003",
  "causal_mechanisms": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "actor": "Meta",
        "type": "Intentional",
        "evidence": "Meta deliberately ignored safety warnings to maximize engagement and profits, directly causing teen mental health issues. The company's reckless disregard for user wellbeing is unconscionable.",
        "explanation": "Critics attribute teen mental health issues directly to Meta's deliberate choices and reckless disregard, assigning moral blame."
      },
      {
        "actor": "EU",
        "type": "Inadvertent",
        "evidence": "EU regulators acknowledge that GDPR, despite its privacy goals, inadvertently created compliance costs that disproportionately burden small companies",
        "explanation": "GDPR's negative effects on small companies are framed as unintended consequences despite good intentions, without moral blame."
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "none",
      "actor": null,
      "reasoning": "All actors with causal attributions are captured"
    }
  ]
}
```

## Common Patterns

### Intentional Patterns
1. **Corporate Malfeasance**: "Company X prioritized profits over safety"
2. **Regulatory Capture**: "Regulator Y deliberately favored industry interests"
3. **Political Blame**: "Party Z's policies directly caused the crisis"
4. **Negligence Claims**: "Their failure to act resulted in harm"

### Inadvertent Patterns
1. **Policy Side Effects**: "The regulation had unintended consequences"
2. **Market Dynamics**: "Competition naturally led to consolidation"
3. **Systemic Issues**: "The system produced unexpected outcomes"
4. **Good Faith Errors**: "Despite best efforts, the approach failed"

## Input Parameters

- `doc_id`: string (unique identifier)
- `title`: string or null (document title)
- `text`: string (the document body)
- `optional_known_actors`: array of strings (pre-identified actors)

## Quality Checks

- Must have clear causal attribution (X caused/led to Y)
- Intentional requires moral blame or responsibility assignment
- Inadvertent requires absence of moral blame
- Generic criticism without causal links should be excluded
- Evidence must support the type classification
