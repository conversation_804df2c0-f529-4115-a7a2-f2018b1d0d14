"""
Prompt templates for document analysis system.

This module contains all the prompt templates extracted from the original 
JavaScript implementation, optimized for Python use.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PromptTemplate:
    """Data class for prompt template information."""
    name: str
    description: str
    template: str
    complexity: str
    estimated_tokens: int
    best_for: List[str]
    variables: List[str]

class PromptTemplates:
    """Manages all prompt templates for document analysis."""
    
    def __init__(self):
        self.stakeholder_categories = [
            'Think Tanks',
            'Government',
            'Media',
            'Corporate',
            'NGOs and Professional Organizations',
            'Universities and Research Institutes',
            'Political Entities',
            'Consultants and Analysts',
            'Legal and Industry-specific Bodies',
            'State Guidelines and Documents',
            'Others'
        ]
        
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, PromptTemplate]:
        """Initialize all prompt templates."""
        return {
            'unified_analysis': PromptTemplate(
                name='unified_analysis',
                description='Comprehensive document analysis with all components',
                template=self._get_unified_analysis_template(),
                complexity='high',
                estimated_tokens=2000,
                best_for=['complete_analysis', 'detailed_reports'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT', 'KNOWN_ACTORS']
            ),
            
            'actor_extraction': PromptTemplate(
                name='actor_extraction',
                description='Extract actors and categorize them',
                template=self._get_actor_extraction_template(),
                complexity='low',
                estimated_tokens=800,
                best_for=['initial_processing', 'entity_identification'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT']
            ),
            
            'relationship_analysis': PromptTemplate(
                name='relationship_analysis',
                description='Analyze relationships between actors',
                template=self._get_relationship_analysis_template(),
                complexity='medium',
                estimated_tokens=1200,
                best_for=['network_analysis', 'relationship_mapping'],
                variables=['DOC_ID', 'KNOWN_ACTORS', 'DOCUMENT_TEXT']
            ),
            
            'portrayal_analysis': PromptTemplate(
                name='portrayal_analysis',
                description='Identify hero/victim/devil portrayals',
                template=self._get_portrayal_analysis_template(),
                complexity='medium',
                estimated_tokens=1000,
                best_for=['narrative_analysis', 'framing_detection'],
                variables=['DOC_ID', 'DOCUMENT_TEXT']
            ),
            
            'issue_scope_analysis': PromptTemplate(
                name='issue_scope_analysis',
                description='Detect issue expansion/containment strategies',
                template=self._get_issue_scope_analysis_template(),
                complexity='medium',
                estimated_tokens=900,
                best_for=['strategic_analysis', 'issue_framing'],
                variables=['DOC_ID', 'DOCUMENT_TEXT']
            ),
            
            'causal_mechanism_analysis': PromptTemplate(
                name='causal_mechanism_analysis',
                description='Identify intentional/inadvertent causal mechanisms',
                template=self._get_causal_mechanism_analysis_template(),
                complexity='medium',
                estimated_tokens=900,
                best_for=['causal_analysis', 'blame_attribution'],
                variables=['DOC_ID', 'DOCUMENT_TEXT']
            ),
            
            'quality_assessment': PromptTemplate(
                name='quality_assessment',
                description='Assess analysis quality and completeness',
                template=self._get_quality_assessment_template(),
                complexity='medium',
                estimated_tokens=1100,
                best_for=['quality_control', 'validation'],
                variables=['DOC_ID', 'DOCUMENT_TEXT', 'ANALYSIS_RESULTS']
            ),
            
            'validation_check': PromptTemplate(
                name='validation_check',
                description='Validate analysis against original document',
                template=self._get_validation_check_template(),
                complexity='medium',
                estimated_tokens=1000,
                best_for=['accuracy_checking', 'completeness_verification'],
                variables=['DOC_ID', 'DOCUMENT_TEXT', 'ANALYSIS_RESULTS']
            ),
            
            'openai_optimized': PromptTemplate(
                name='openai_optimized',
                description='OpenAI-optimized version of unified analysis',
                template=self._get_openai_optimized_template(),
                complexity='high',
                estimated_tokens=1800,
                best_for=['openai_models', 'fast_processing'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT']
            ),
            
            'anthropic_optimized': PromptTemplate(
                name='anthropic_optimized',
                description='Anthropic-optimized version of unified analysis',
                template=self._get_anthropic_optimized_template(),
                complexity='high',
                estimated_tokens=1900,
                best_for=['anthropic_models', 'detailed_analysis'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT']
            ),
            
            'zhipu_optimized': PromptTemplate(
                name='zhipu_optimized',
                description='Chinese-optimized version for Zhipu models',
                template=self._get_zhipu_optimized_template(),
                complexity='high',
                estimated_tokens=2000,
                best_for=['zhipu_models', 'chinese_content'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT']
            ),
            
            'quick_summary': PromptTemplate(
                name='quick_summary',
                description='Quick summary of key actors and main points',
                template=self._get_quick_summary_template(),
                complexity='low',
                estimated_tokens=600,
                best_for=['rapid_overview', 'key_points'],
                variables=['DOC_ID', 'TITLE', 'DOCUMENT_TEXT']
            )
        }
    
    def _get_unified_analysis_template(self) -> str:
        """Get the unified analysis template."""
        return f"""You are an expert information extraction and narrative analysis model. Analyze the following document and return ONLY valid JSON matching the specified schema.

## TASK OBJECTIVES
1. Extract actors (real persons/organizations only) and their relationships
2. Identify narrative portrayals (hero, victim, devil) with evidence
3. Detect issue scope strategies (expansion/containment) with evidence  
4. Identify causal mechanisms (intentional/inadvertent) with evidence
5. Provide AI decisions for actor management if needed

## CRITICAL DEFINITIONS
- **Actors**: Real persons or organizations only. No abstract/virtual roles
- **Relationship**: Directed interaction between two actors (e.g., "regulates", "lobbies", "collaborates")
- **Portrayals**:
  • Hero: Actor framed as able to fix problems, self-promotional, emphasizing protective role
  • Victim: Actor framed as harmed by narratives/actions, not merely policy limitations
  • Devil: Opponents framed as malicious or more evil than reality, exaggerating motives
- **Issue expansion**: Deliberate broadening of scope, diffusing costs across many while benefits concentrate to few
- **Issue containment**: Deliberate narrowing of scope to specialists, limiting salience
- **Causal mechanisms**:
  • Intentional: Assigns blame deliberately to harm reputations or shift responsibility
  • Inadvertent: Attributes problems to unintended consequences of policies

## EXTRACTION RULES
- Extract specific, named actors (e.g., "FTC", "OpenAI"); avoid categories like "policymakers"
- Maintain directional consistency for relationships (e.g., "A regulates B")
- Split multi-actor mentions into multiple pairwise relationships
- Use concise verb phrases for relationships
- Evidence must be direct quotations or tight paraphrases from text
- If no valid findings, set skipped=true with reason

## EXCLUSION RULES
- Non-real/virtual roles; hypothetical statements ("should", "could")
- Flat factual descriptions without narrative framing
- Generic concerns without named actors; indirect framings
- Pure insights/opinions; drawbacks without framing
- Statements without blame for causal mechanisms

## STAKEHOLDER CATEGORIES
{', '.join(self.stakeholder_categories)}

## OUTPUT SCHEMA
{{
  "doc_id": "string",
  "title": "string|null",
  "actors": [
    {{
      "name": "string",
      "stakeholder_category": "category from list above",
      "notes": "string|null"
    }}
  ],
  "relationships": {{
    "skipped": "boolean",
    "skip_reason": "string|null", 
    "items": [
      {{
        "a_name": "string",
        "b_name": "string",
        "relationship": "string",
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null", 
        "a_type": "stakeholder category",
        "b_type": "stakeholder category",
        "evidence": "quote or text span"
      }}
    ]
  }},
  "portrayals": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "quote",
        "explanation": "1-2 sentences grounding the portrayal"
      }}
    ],
    "shifts": {{
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean", 
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }}
  }},
  "issue_scope": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "actor": "string", 
        "type": "Issue expansion|Issue containment",
        "evidence": "quote",
        "explanation": "1-2 sentences explaining the strategy"
      }}
    ]
  }},
  "causal_mechanisms": {{
    "skipped": "boolean",
    "skip_reason": "string|null", 
    "items": [
      {{
        "actor": "string",
        "type": "Intentional|Inadvertent",
        "evidence": "quote or passage",
        "explanation": "1-2 sentences on blame assignment"
      }}
    ]
  }},
  "ai_decisions": [
    {{
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }}
  ]
}}

## INPUT DATA
doc_id: {{{{DOC_ID}}}}
title: {{{{TITLE}}}}
text: |
{{{{DOCUMENT_TEXT}}}}
known_actors: {{{{KNOWN_ACTORS}}}}

Return ONLY valid JSON. No explanations outside the schema."""
    
    def _get_actor_extraction_template(self) -> str:
        """Get actor extraction template from original file."""
        return """You are an expert entity extraction specialist. Your task is to identify ALL real persons and organizations mentioned in the document.

CRITICAL REQUIREMENTS:
- Extract ONLY real persons and organizations (no abstract concepts, roles, or categories)
- Include every named entity regardless of how minor they seem
- For each actor, determine their stakeholder category from the provided list
- Provide brief notes about who they are if available
- Do NOT include: generic roles, abstract concepts, or unnamed groups

STAKEHOLDER CATEGORIES:
["Think Tanks","Government","Media","Corporate","NGOs and Professional Organizations","Universities and Research Institutes","Political Entities","Consultants and Analysts","Legal and Industry-specific Bodies","State Guidelines and Documents","Others"]

EXAMPLES:
✅ GOOD: "FTC", "Google", "Microsoft", "Consumer advocacy groups", "Senator Smith"
❌ BAD: "policymakers", "regulators", "the public", "companies"

Return ONLY valid JSON with this structure:
{
  "actors": [
    {
      "name": "string",
      "stakeholder_category": "category from list",
      "notes": "string|null"
    }
  ]
}"""
    
    def _get_relationship_analysis_template(self) -> str:
        """Get relationship analysis template from original file."""
        return """You are an expert relationship analyst. Your task is to analyze relationships between the provided actors.

ANALYSIS REQUIREMENTS:
- Analyze ALL pairwise relationships between actors
- Identify directed relationships (A regulates B, B lobbies A)
- Use specific verb phrases for relationships
- Character assignments: hero, villain, victim (or null)
- Evidence must be direct quotes or tight paraphrases
- Consider relationship directionality and power dynamics

RELATIONSHIP TYPES:
- Regulatory: regulates, oversees, monitors, enforces
- Opposition: criticizes, opposes, challenges, questions
- Support: supports, endorses, advocates for, defends
- Collaboration: works with, partners with, collaborates with
- Influence: lobbies, pressures, persuades, influences
- Conflict: conflicts with, competes with, disputes with

CHARACTER DEFINITIONS:
- HERO: Takes positive action, solves problems, protects others
- VILLAIN: Acts maliciously, opposes progress, harms others  
- VICTIM: Suffers harm, bears consequences, is affected negatively

Return ONLY valid JSON with this structure:
{
  "relationships": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "a_name": "string",
        "b_name": "string", 
        "relationship": "string",
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null",
        "a_type": "stakeholder category",
        "b_type": "stakeholder category", 
        "evidence": "quote from text"
      }
    ]
  }
}"""
    
    def _get_portrayal_analysis_template(self) -> str:
        """Get portrayal analysis template from original file."""
        return """You are an expert narrative analysis specialist. Your task is to detect hero/victim/devil portrayals.

PORTRAYAL DEFINITIONS:
- HERO: Actor framed as able to fix problems, self-promotional, protective role
- VICTIM: Actor framed as harmed or bearing consequences from narratives/actions  
- DEVIL: Opponents framed as malicious or more evil than they are

ANALYSIS REQUIREMENTS:
- Look for specific framing language and narrative positioning
- Evidence must be direct quotes showing the portrayal
- Provide 1-2 sentence explanations grounding the portrayal
- Detect angel/devil shifts if present

FRAMING INDICATORS:
HERO LANGUAGE:
- "took action", "stepped in", "protected", "saved", "championed"
- "leading the way", "pioneering", "innovative solution"
- "advocate for", "defender of", "champion for"

VICTIM LANGUAGE:
- "harmed by", "suffered", "affected by", "bearing the cost"
- "vulnerable to", "at risk from", "threatened by"
- "disproportionately affected", "bearing the burden"

DEVIL LANGUAGE:
- "malicious", "evil", "corrupt", "selfish", "greedy"
- "exploiting", "manipulating", "deceiving", "endangering"
- "irresponsible", "reckless", "harmful", "dangerous"

NARRATIVE SHIFTS:
- ANGEL SHIFT: Framing oneself or allies as heroes
- DEVIL SHIFT: Framing opponents as devils or villains

Return ONLY valid JSON with this structure:
{
  "portrayals": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "quote",
        "explanation": "1-2 sentences grounding why this is Hero/Victim/Devil"
      }
    ],
    "shifts": {
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean", 
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }
  }
}"""
    
    def _get_issue_scope_analysis_template(self) -> str:
        """Get issue scope analysis template from original file."""
        return """You are an expert issue scope analyst. Your task is to detect issue expansion and containment strategies.

SCOPE STRATEGY DEFINITIONS:
- ISSUE EXPANSION: Deliberate broadening of scope/audience, diffusing costs across many while benefits concentrate to few
- ISSUE CONTAINMENT: Deliberate narrowing of scope/audience to specialists, limiting salience

ANALYSIS REQUIREMENTS:
- Identify deliberate attempts to broaden or narrow issue scope
- Look for language that expands/contracts the audience or implications
- Evidence must show the strategic broadening/narrowing
- Provide explanation of the scope strategy

ISSUE EXPANSION INDICATORS:
- "affects everyone", "national crisis", "global impact"
- "entire industry", "all consumers", "widespread consequences"
- "systemic problem", "broader implications", "far-reaching"
- Moving from specific to general, from local to universal

ISSUE CONTAINMENT INDICATORS:
- "technical matter", "specialized issue", "expert domain"
- "limited impact", "isolated incident", "specific case"
- "internal matter", "operational details", "procedural"
- Moving from general to specific, from universal to local

STRATEGIC QUESTIONS:
- Who benefits from broadening/narrowing the scope?
- Who bears costs in each scenario?
- What language is used to include/exclude stakeholders?
- How does the scope change affect the perceived importance?

Return ONLY valid JSON with this structure:
{
  "issue_scope": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "actor": "string",
        "type": "Issue expansion|Issue containment",
        "evidence": "quote",
        "explanation": "1-2 sentences explaining the deliberate broadening/narrowing"
      }
    ]
  }
}"""
    
    def _get_causal_mechanism_analysis_template(self) -> str:
        """Get causal mechanism analysis template from original file."""
        return """You are an expert causal mechanism analyst. Your task is to detect causal attributions.

CAUSAL MECHANISM DEFINITIONS:
- INTENTIONAL: Assigns blame deliberately to harm others' reputations or shift responsibility
- INADVERTENT: Attributes problems to unintended consequences of a policy

ANALYSIS REQUIREMENTS:
- Identify blame assignment and causal reasoning
- Distinguish between intentional blame and unintended consequences
- Evidence must show the causal reasoning pattern
- Provide explanation of the causal mechanism

INTENTIONAL BLAME INDICATORS:
- "deliberately", "intentionally", "knowingly", "purposely"
- "responsible for", "to blame for", "caused the problem"
- "malicious intent", "deliberate action", "conscious decision"
- Language suggesting moral responsibility and agency

INADVERTENT CONSEQUENCE INDICATORS:
- "unintended", "unforeseen", "unexpected", "inadvertent"
- "side effect", "unintentional consequence", "unforeseen outcome"
- "despite good intentions", "well-meaning but..."
- Language suggesting systemic or structural causes

CAUSAL ANALYSIS QUESTIONS:
- Is the cause attributed to human agency or systemic factors?
- Is there language suggesting intent or lack thereof?
- How is responsibility distributed among actors?
- What type of causal reasoning is being employed?

Return ONLY valid JSON with this structure:
{
  "causal_mechanisms": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "actor": "string",
        "type": "Intentional|Inadvertent", 
        "evidence": "quote or short passage",
        "explanation": "1-2 sentences on blame assignment or unintended consequences"
      }
    ]
  }
}"""
    
    def _get_quality_assessment_template(self) -> str:
        """Get quality assessment template."""
        return """Assess the quality and completeness of this document analysis. Return ONLY valid JSON.

## QUALITY METRICS
- **Entity Coverage**: Are all significant actors identified and categorized?
- **Relationship Completeness**: Are key relationships captured with evidence?
- **Narrative Depth**: Are portrayals and strategies well-identified?
- **Evidence Quality**: Is evidence sufficient and well-grounded?
- **Consistency**: Are findings consistent across the document?

## OUTPUT SCHEMA
{
  "doc_id": "{{DOC_ID}}",
  "quality_assessment": {
    "overall_score": 0.0 to 1.0,
    "entity_coverage": 0.0 to 1.0,
    "relationship_completeness": 0.0 to 1.0, 
    "narrative_depth": 0.0 to 1.0,
    "evidence_quality": 0.0 to 1.0,
    "consistency": 0.0 to 1.0,
    "strengths": ["string"],
    "weaknesses": ["string"],
    "recommendations": ["string"]
  }
}

## DOCUMENT
{{DOCUMENT_TEXT}}

{{#ANALYSIS_RESULTS}}
## ANALYSIS RESULTS
{{{ANALYSIS_RESULTS}}}
{{/ANALYSIS_RESULTS}}

Return ONLY valid JSON."""
    
    def _get_validation_check_template(self) -> str:
        """Get validation check template."""
        return """Validate this analysis against the original document for accuracy and completeness. Return ONLY valid JSON.

## VALIDATION CRITERIA
- **Actor Accuracy**: Are all actors correctly identified and categorized?
- **Relationship Accuracy**: Are relationships correctly captured and directed?
- **Evidence Validity**: Does evidence accurately reflect the source text?
- **Completeness**: Are any significant elements missing?
- **Schema Compliance**: Does output match required schema?

## OUTPUT SCHEMA
{
  "doc_id": "{{DOC_ID}}",
  "validation": {
    "passed": "boolean",
    "errors": [
      {
        "type": "actor|relationship|evidence|completeness|schema",
        "message": "string",
        "severity": "low|medium|high"
      }
    ],
    "warnings": ["string"],
    "corrections_needed": "boolean",
    "suggestions": ["string"]
  }
}

## DOCUMENT
{{DOCUMENT_TEXT}}

{{#ANALYSIS_RESULTS}}
## ANALYSIS RESULTS
{{{ANALYSIS_RESULTS}}}
{{/ANALYSIS_RESULTS}}

Return ONLY valid JSON."""
    
    def _get_openai_optimized_template(self) -> str:
        """Get OpenAI optimized template."""
        return """You are an expert information extraction and narrative analysis model. Analyze this document and return ONLY valid JSON.

Focus on extracting actors, relationships, portrayals, issue scope strategies, and causal mechanisms with precise evidence.

## OUTPUT SCHEMA
{
  "doc_id": "{{DOC_ID}}",
  "title": "{{TITLE}}", 
  "actors": [{"name": "string", "stakeholder_category": "string", "notes": "string|null"}],
  "relationships": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"a_name": "string", "b_name": "string", "relationship": "string", "a_character": "hero|villain|victim|null", "b_character": "hero|villain|victim|null", "a_type": "string", "b_type": "string", "evidence": "string"}]},
  "portrayals": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Hero|Victim|Devil", "evidence": "string", "explanation": "string"}], "shifts": {"angel_shift_present": "boolean", "devil_shift_present": "boolean", "angel_shift_evidence": "string|null", "devil_shift_evidence": "string|null"}},
  "issue_scope": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Issue expansion|Issue containment", "evidence": "string", "explanation": "string"}]},
  "causal_mechanisms": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Intentional|Inadvertent", "evidence": "string", "explanation": "string"}]},
  "ai_decisions": [{"action": "add_actor|remove_actor|none", "actor": "string|null", "reasoning": "string"}]
}

## DOCUMENT
{{DOCUMENT_TEXT}}

Return ONLY valid JSON."""
    
    def _get_anthropic_optimized_template(self) -> str:
        """Get Anthropic optimized template."""
        return """I need you to analyze a document and extract specific information about actors, relationships, and narrative patterns.

Please analyze this document and return a structured JSON response with the following components:

1. **Actors**: Extract all real persons and organizations, categorizing them as: Think Tanks, Government, Media, Corporate, NGOs and Professional Organizations, Universities and Research Institutes, Political Entities, Consultants and Analysts, Legal and Industry-specific Bodies, State Guidelines and Documents, or Others.

2. **Relationships**: Identify directed relationships between actors, noting how they interact (regulates, lobbies, collaborates, etc.) and any character portrayals (hero, villain, victim).

3. **Portrayals**: Look for hero/victim/devil framing - where actors position themselves as problem-solvers, victims of harm, or opponents as malicious.

4. **Issue Scope**: Identify attempts to broaden (issue expansion) or narrow (issue containment) the scope of the issue.

5. **Causal Mechanisms**: Find intentional blame assignment or attribution of problems to unintended consequences.

## OUTPUT SCHEMA
{
  "doc_id": "{{DOC_ID}}",
  "title": "{{TITLE}}",
  "actors": [{"name": "string", "stakeholder_category": "string", "notes": "string|null"}],
  "relationships": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"a_name": "string", "b_name": "string", "relationship": "string", "a_character": "hero|villain|victim|null", "b_character": "hero|villain|victim|null", "a_type": "string", "b_type": "string", "evidence": "string"}]},
  "portrayals": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Hero|Victim|Devil", "evidence": "string", "explanation": "string"}], "shifts": {"angel_shift_present": "boolean", "devil_shift_present": "boolean", "angel_shift_evidence": "string|null", "devil_shift_evidence": "string|null"}},
  "issue_scope": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Issue expansion|Issue containment", "evidence": "string", "explanation": "string"}]},
  "causal_mechanisms": {"skipped": "boolean", "skip_reason": "string|null", "items": [{"actor": "string", "type": "Intentional|Inadvertent", "evidence": "string", "explanation": "string"}]},
  "ai_decisions": [{"action": "add_actor|remove_actor|none", "actor": "string|null", "reasoning": "string"}]
}

## DOCUMENT
{{DOCUMENT_TEXT}}

Return your response as valid JSON only."""
    
    def _get_zhipu_optimized_template(self) -> str:
        """Get Zhipu optimized template."""
        categories = ', '.join(self.stakeholder_categories)
        return f"""请分析以下文档，提取关键信息并返回JSON格式的结果。

## 分析任务
1. **提取行动者**: 识别文档中的真实人物和组织机构
2. **分析关系**: 确定行动者之间的 directed 关系（如监管、游说、合作等）
3. **识别角色定位**: 发现英雄、受害者、恶魔等角色塑造
4. **问题范围策略**: 识别问题扩大化或缩小化策略
5. **因果机制**: 分析有意或无意的归因机制

## 关键定义
- **行动者**: 真实的人物或组织，不包括抽象角色
- **关系**: 两个行动者之间的 directed 互动
- **角色塑造**: 
  • 英雄: 塑造为解决问题者
  • 受害者: 塑造为受到伤害者  
  • 恶魔: 对手被塑造为恶意者
- **问题扩大化**: 故意扩大问题范围，分散成本，集中收益
- **问题缩小化**: 限制问题范围，降低显著性
- **因果机制**:
  • 有意: 故意归咎于他人
  • 无意: 归因于政策意外后果

## 利益相关者类别
{categories}

## 输出格式 (OUTPUT SCHEMA)
{{
  "doc_id": "{{{{DOC_ID}}}}",
  "title": "{{{{TITLE}}}}",
  "actors": [{{"name": "string", "stakeholder_category": "string", "notes": "string|null"}}],
  "relationships": {{"skipped": "boolean", "skip_reason": "string|null", "items": [{{"a_name": "string", "b_name": "string", "relationship": "string", "a_character": "hero|villain|victim|null", "b_character": "hero|villain|victim|null", "a_type": "string", "b_type": "string", "evidence": "string"}}]}},
  "portrayals": {{"skipped": "boolean", "skip_reason": "string|null", "items": [{{"actor": "string", "type": "Hero|Victim|Devil", "evidence": "string", "explanation": "string"}}], "shifts": {{"angel_shift_present": "boolean", "devil_shift_present": "boolean", "angel_shift_evidence": "string|null", "devil_shift_evidence": "string|null"}}}},
  "issue_scope": {{"skipped": "boolean", "skip_reason": "string|null", "items": [{{"actor": "string", "type": "Issue expansion|Issue containment", "evidence": "string", "explanation": "string"}}]}},
  "causal_mechanisms": {{"skipped": "boolean", "skip_reason": "string|null", "items": [{{"actor": "string", "type": "Intentional|Inadvertent", "evidence": "string", "explanation": "string"}}]}},
  "ai_decisions": [{{"action": "add_actor|remove_actor|none", "actor": "string|null", "reasoning": "string"}}]
}}

## 文档内容
{{{{DOCUMENT_TEXT}}}}

请只返回有效的JSON结果。"""
    
    def _get_quick_summary_template(self) -> str:
        """Get quick summary template for rapid overview."""
        return """You are an expert document analyst. Provide a quick summary of the key actors and main points from this document.

## TASK
Extract the most important actors and provide a brief overview of the document's main topic and purpose.

## REQUIREMENTS
- Focus on the main actors (organizations and key individuals)
- Identify the primary topic or issue
- Keep it concise and high-level
- Only include the most significant information

## OUTPUT SCHEMA
{
  "doc_id": "{{DOC_ID}}",
  "title": "{{TITLE}}",
  "summary": {
    "main_topic": "Brief description of the main topic",
    "key_actors": [
      {
        "name": "string",
        "stakeholder_category": "Government|Corporate|Media|NGOs and Professional Organizations|Others",
        "role": "Brief description of their role in this document"
      }
    ],
    "key_points": ["string"],
    "document_type": "news|report|policy|announcement|other"
  }
}

## DOCUMENT
{{DOCUMENT_TEXT}}

Return ONLY valid JSON."""
    
    def get_template(self, template_name: str) -> PromptTemplate:
        """Get a specific template by name."""
        return self.templates.get(template_name, self.templates['unified_analysis'])
    
    def get_all_templates(self) -> Dict[str, PromptTemplate]:
        """Get all available templates."""
        return self.templates
    
    def get_template_names(self) -> List[str]:
        """Get all available template names."""
        return list(self.templates.keys())
    
    def get_templates_by_category(self) -> Dict[str, List[str]]:
        """Get templates organized by category."""
        return {
            'core_analysis': [
                'unified_analysis',
                'actor_extraction',
                'relationship_analysis'
            ],
            'narrative_analysis': [
                'portrayal_analysis',
                'issue_scope_analysis',
                'causal_mechanism_analysis'
            ],
            'quality_assessment': [
                'quality_assessment',
                'validation_check'
            ],
            'provider_optimized': [
                'openai_optimized',
                'anthropic_optimized',
                'zhipu_optimized'
            ]
        }
    
    def format_template(self, template_name: str, variables: Dict[str, str]) -> str:
        """Format a template with variables."""
        template = self.get_template(template_name)
        formatted = template.template
        
        # Validate required variables
        missing_vars = []
        for required_var in template.variables:
            if required_var not in variables:
                missing_vars.append(required_var)
        
        if missing_vars:
            logger.warning(f"Missing required variables for template {template_name}: {missing_vars}")
        
        # Replace variables
        for var_name, var_value in variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            formatted = formatted.replace(placeholder, str(var_value))
        
        # Check for unreplaced placeholders
        import re
        unreplaced = re.findall(r'{{([^{}]+)}}', formatted)
        if unreplaced:
            logger.warning(f"Unreplaced placeholders in template {template_name}: {unreplaced}")
        
        return formatted
    
    def save_prompts_to_files(self, output_dir: str) -> None:
        """Save all prompts to individual files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for template_name, template in self.templates.items():
            file_path = output_path / f"{template_name}.txt"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(template.template)