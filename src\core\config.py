"""
Configuration management for the document analysis system.
"""

import os
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class LLMConfig:
    """Configuration for LLM providers."""
    provider: str = "zhipuai"  # openai, anthropic, zhipuai
    model: str = "glm-4"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = 0.0
    max_tokens: int = 4000
    timeout: int = 60
    max_retries: int = 3

@dataclass
class AnalysisConfig:
    """Configuration for analysis parameters."""
    stakeholder_categories: List[str] = field(default_factory=lambda: [
        "Think Tanks",
        "Government", 
        "Media",
        "Corporate",
        "NGOs and Professional Organizations",
        "Universities and Research Institutes",
        "Political Entities",
        "Consultants and Analysts",
        "Legal and Industry-specific Bodies",
        "State Guidelines and Documents",
        "Others"
    ])
    
    # Analysis components to run
    enable_actor_extraction: bool = True
    enable_relationship_analysis: bool = True
    enable_portrayal_analysis: bool = True
    enable_issue_scope_analysis: bool = True
    enable_causal_mechanism_analysis: bool = True
    enable_quality_assessment: bool = True
    
    # Quality thresholds
    min_confidence_score: float = 0.7
    min_evidence_quality: float = 0.6
    
    # Processing settings
    max_document_length: int = 10000
    chunk_size: int = 2000
    overlap_size: int = 200

@dataclass
class OutputConfig:
    """Configuration for output formatting."""
    include_raw_text: bool = False
    include_confidence_scores: bool = True
    include_quality_metrics: bool = True
    format_schema_validation: bool = True
    pretty_print: bool = True
    
    # Output formats
    output_json: bool = True
    output_csv: bool = False
    output_html: bool = False

@dataclass
class Config:
    """Main configuration class."""
    llm: LLMConfig = field(default_factory=LLMConfig)
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    
    # File paths
    data_dir: str = "data"
    output_dir: str = "output"
    prompts_dir: str = "prompts"
    logs_dir: str = "logs"
    
    # Debug and testing
    debug_mode: bool = False
    use_mock_llm: bool = False
    save_intermediate_results: bool = False
    
    def __post_init__(self):
        """Post-initialization to load environment variables."""
        # Load API key from environment variables
        self.llm.api_key = os.getenv('ZHIPUAI_API_KEY') or os.getenv('OPENAI_API_KEY') or os.getenv('ANTHROPIC_API_KEY')
        
        # Load other LLM settings from environment
        self.llm.provider = os.getenv('LLM_PROVIDER', self.llm.provider)
        self.llm.model = os.getenv('LLM_MODEL', self.llm.model)
        self.use_mock_llm = os.getenv('USE_MOCK_LLM', 'false').lower() == 'true'
    
    @classmethod
    def from_file(cls, config_path: str) -> 'Config':
        """Load configuration from JSON file."""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls.from_dict(config_data)
    
    @classmethod
    def from_dict(cls, config_data: Dict[str, Any]) -> 'Config':
        """Create configuration from dictionary."""
        llm_config = LLMConfig(**config_data.get('llm', {}))
        analysis_config = AnalysisConfig(**config_data.get('analysis', {}))
        output_config = OutputConfig(**config_data.get('output', {}))
        
        # Update LLM config with environment variables
        llm_config.api_key = os.getenv('OPENAI_API_KEY') or os.getenv('ANTHROPIC_API_KEY') or os.getenv('ZHIPUAI_API_KEY')
        
        return cls(
            llm=llm_config,
            analysis=analysis_config,
            output=output_config,
            **{k: v for k, v in config_data.items() 
               if k not in ['llm', 'analysis', 'output']}
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'llm': self.llm.__dict__,
            'analysis': self.analysis.__dict__,
            'output': self.output.__dict__,
            'data_dir': self.data_dir,
            'output_dir': self.output_dir,
            'prompts_dir': self.prompts_dir,
            'logs_dir': self.logs_dir,
            'debug_mode': self.debug_mode,
            'use_mock_llm': self.use_mock_llm,
            'save_intermediate_results': self.save_intermediate_results
        }
    
    def save_to_file(self, config_path: str) -> None:
        """Save configuration to JSON file."""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    def create_directories(self) -> None:
        """Create necessary directories."""
        for dir_path in [self.data_dir, self.output_dir, self.prompts_dir, self.logs_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate LLM configuration
        if self.llm.provider not in ['openai', 'anthropic', 'zhipuai']:
            errors.append(f"Invalid LLM provider: {self.llm.provider}")
        
        if not self.llm.api_key and not self.use_mock_llm:
            errors.append("API key is required when not using mock LLM")
        
        if not (0 <= self.llm.temperature <= 2):
            errors.append(f"Temperature must be between 0 and 2, got: {self.llm.temperature}")
        
        if self.llm.max_tokens <= 0:
            errors.append(f"Max tokens must be positive, got: {self.llm.max_tokens}")
        
        # Validate analysis configuration
        if self.analysis.min_confidence_score < 0 or self.analysis.min_confidence_score > 1:
            errors.append(f"Min confidence score must be between 0 and 1, got: {self.analysis.min_confidence_score}")
        
        if self.analysis.max_document_length <= 0:
            errors.append(f"Max document length must be positive, got: {self.analysis.max_document_length}")
        
        return errors