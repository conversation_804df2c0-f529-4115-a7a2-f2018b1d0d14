<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试文件选择问题</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .debug-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🔧 调试文件选择问题</h1>
        
        <div class="debug-panel">
            <h3>步骤1：选择文档</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>选择文件</h5>
                    <div class="mb-3">
                        <div class="input-group">
                            <input type="file" class="form-control" id="fileInput" accept=".txt,.pdf,.docx,.doc,.xlsx,.xls,.csv" multiple>
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-file-upload"></i> 浏览
                            </button>
                        </div>
                        <small class="text-muted">支持格式: TXT, PDF, DOC, DOCX, XLS, XLSX, CSV</small>
                    </div>
                    
                    <h5>已选文件</h5>
                    <div class="document-list" id="selectedFilesList" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px;">
                        <!-- 已选择的文件列表 -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>文档预览</h5>
                    <div class="response-area" id="documentPreview" style="min-height: 200px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; font-family: monospace; white-space: pre-wrap;">
                        请选择一个文档进行预览
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-primary btn-step" onclick="testNextStep()" id="nextStep1" disabled>
                    下一步 <i class="fas fa-arrow-right"></i>
                </button>
                <button class="btn btn-success ms-2" onclick="testVariables()">
                    测试变量
                </button>
                <button class="btn btn-info ms-2" onclick="clearConsole()">
                    清空控制台
                </button>
            </div>
        </div>
        
        <div class="debug-panel">
            <h3>控制台输出</h3>
            <div id="consoleOutput" class="console-output">等待调试...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedDocument = null;
        let selectedFiles = [];
        let fileContents = {};
        let currentStep = 1;
        
        // 控制台输出
        function log(message) {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.textContent += `[${timestamp}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message); // 同时输出到浏览器控制台
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').textContent = '';
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面已加载');
            setupFileInput();
        });
        
        // 设置文件输入
        function setupFileInput() {
            log('设置文件输入监听器');
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', function(e) {
                log('文件选择事件触发');
                handleFileSelect(e.target.files);
            });
        }
        
        // 处理文件选择
        function handleFileSelect(files) {
            log(`handleFileSelect 被调用，文件数量: ${files.length}`);
            
            selectedFiles = [];
            fileContents = {};
            
            const selectedFilesList = document.getElementById('selectedFilesList');
            selectedFilesList.innerHTML = '';
            
            if (files.length === 0) {
                log('没有选择文件');
                document.getElementById('nextStep1').disabled = true;
                return;
            }
            
            log(`开始处理 ${files.length} 个文件`);
            
            // 处理每个文件
            Array.from(files).forEach((file, index) => {
                log(`处理文件 ${index + 1}: ${file.name}`);
                
                const fileId = `file_${index}_${Date.now()}`;
                const fileInfo = {
                    id: fileId,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    file: file
                };
                
                selectedFiles.push(fileInfo);
                log(`添加文件到列表: ${file.name}`);
                
                // 创建文件项
                const fileItem = document.createElement('div');
                fileItem.className = 'document-item';
                fileItem.style.cssText = 'padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px; cursor: pointer;';
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <strong>${file.name}</strong>
                            <small class="text-muted d-block">${formatFileSize(file.size)} • ${getFileTypeName(file.type)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFile('${fileId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                fileItem.onclick = (e) => {
                    if (!e.target.closest('button')) {
                        log(`点击文件项: ${file.name}`);
                        selectDocument(fileInfo);
                    }
                };
                
                selectedFilesList.appendChild(fileItem);
                
                // 读取文件内容
                readFileContent(file, fileId);
            });
            
            // 默认选择第一个文件
            if (selectedFiles.length > 0) {
                log(`自动选择第一个文件: ${selectedFiles[0].name}`);
                selectDocument(selectedFiles[0]);
            }
        }
        
        // 选择文档
        function selectDocument(doc) {
            log(`selectDocument 被调用，文档: ${doc.name}`);
            selectedDocument = doc;
            
            // 更新UI
            document.querySelectorAll('.document-item').forEach(item => {
                item.classList.remove('selected');
                item.style.backgroundColor = '';
            });
            
            // 找到对应的文件项并添加选中状态
            const fileItems = document.querySelectorAll('.document-item');
            fileItems.forEach(item => {
                if (item.textContent.includes(doc.name)) {
                    item.classList.add('selected');
                    item.style.backgroundColor = '#007bff';
                    item.style.color = 'white';
                }
            });
            
            // 显示文档预览
            const content = fileContents[doc.id] || `正在加载 ${doc.name} 的内容...`;
            document.getElementById('documentPreview').textContent = content;
            
            // 启用下一步按钮
            const nextButton = document.getElementById('nextStep1');
            if (nextButton) {
                nextButton.disabled = false;
                log('下一步按钮已启用');
            } else {
                log('错误: 找不到下一步按钮');
            }
        }
        
        // 读取文件内容
        function readFileContent(file, fileId) {
            log(`开始读取文件内容: ${file.name}`);
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                let content = e.target.result;
                log(`文件读取完成: ${file.name}, 内容长度: ${content.length}`);
                
                // 根据文件类型处理内容
                if (file.type === 'application/pdf') {
                    content = `PDF文件: ${file.name}\\n大小: ${formatFileSize(file.size)}\\n\\nPDF内容解析需要后端支持`;
                } else if (file.name.match(/\.(doc|docx)$/)) {
                    content = `Word文档: ${file.name}\\n大小: ${formatFileSize(file.size)}\\n\\nWord文档内容解析需要后端支持`;
                } else if (file.name.match(/\.(xls|xlsx|csv)$/)) {
                    content = `Excel文件: ${file.name}\\n大小: ${formatFileSize(file.size)}\\n\\nExcel文件内容解析需要后端支持`;
                } else {
                    // 文本文件，直接显示内容（限制长度）
                    if (content.length > 1000) {
                        content = content.substring(0, 1000) + '\\n\\n... [内容过长，只显示前1000字符]';
                    }
                }
                
                fileContents[fileId] = content;
                log(`文件内容已保存: ${file.name}`);
                
                // 如果这是当前选中的文件，更新预览
                if (selectedDocument && selectedDocument.id === fileId) {
                    document.getElementById('documentPreview').textContent = content;
                    log(`更新预览内容: ${file.name}`);
                }
            };
            
            reader.onerror = function() {
                log(`文件读取失败: ${file.name}`);
                fileContents[fileId] = `无法读取文件: ${file.name}`;
                if (selectedDocument && selectedDocument.id === fileId) {
                    document.getElementById('documentPreview').textContent = fileContents[fileId];
                }
            };
            
            // 根据文件类型选择读取方式
            if (file.type.startsWith('text/') || file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
                log(`以文本方式读取文件: ${file.name}`);
                reader.readAsText(file);
            } else {
                log(`以DataURL方式读取文件: ${file.name}`);
                reader.readAsDataURL(file);
            }
        }
        
        // 测试下一步
        function testNextStep() {
            log('testNextStep 被调用');
            log(`当前状态:`);
            log(`  - currentStep: ${currentStep}`);
            log(`  - selectedDocument: ${selectedDocument ? selectedDocument.name : 'null'}`);
            log(`  - selectedFiles.length: ${selectedFiles.length}`);
            log(`  - Object.keys(fileContents).length: ${Object.keys(fileContents).length}`);
            
            if (!selectedDocument) {
                alert('请先选择一个文档');
                return;
            }
            
            alert(`成功！当前选中文档: ${selectedDocument.name}\\n文件大小: ${formatFileSize(selectedDocument.size)}\\n文件类型: ${selectedDocument.type}`);
        }
        
        // 测试变量
        function testVariables() {
            log('=== 变量状态 ===');
            log(`selectedDocument: ${JSON.stringify(selectedDocument, null, 2)}`);
            log(`selectedFiles: ${selectedFiles.length} 个文件`);
            log(`fileContents: ${Object.keys(fileContents).length} 个内容`);
            log(`currentStep: ${currentStep}`);
            
            alert('变量状态已输出到控制台');
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 获取文件类型名称
        function getFileTypeName(mimeType) {
            if (mimeType.startsWith('text/')) return '文本文件';
            if (mimeType === 'application/pdf') return 'PDF文档';
            if (mimeType.includes('word')) return 'Word文档';
            if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'Excel表格';
            if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return 'Word文档';
            if (mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') return 'Excel表格';
            return '未知类型';
        }
        
        // 移除文件
        function removeFile(fileId) {
            log(`移除文件: ${fileId}`);
            selectedFiles = selectedFiles.filter(f => f.id !== fileId);
            delete fileContents[fileId];
            
            // 重新渲染文件列表
            const selectedFilesList = document.getElementById('selectedFilesList');
            selectedFilesList.innerHTML = '';
            
            selectedFiles.forEach(fileInfo => {
                const fileItem = document.createElement('div');
                fileItem.className = 'document-item';
                fileItem.style.cssText = 'padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px; cursor: pointer;';
                if (selectedDocument && selectedDocument.id === fileInfo.id) {
                    fileItem.classList.add('selected');
                    fileItem.style.backgroundColor = '#007bff';
                    fileItem.style.color = 'white';
                }
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <strong>${fileInfo.name}</strong>
                            <small class="text-muted d-block">${formatFileSize(fileInfo.size)} • ${getFileTypeName(fileInfo.type)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFile('${fileInfo.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                fileItem.onclick = (e) => {
                    if (!e.target.closest('button')) {
                        selectDocument(fileInfo);
                    }
                };
                selectedFilesList.appendChild(fileItem);
            });
            
            // 如果没有文件了，禁用下一步
            if (selectedFiles.length === 0) {
                selectedDocument = null;
                document.getElementById('documentPreview').textContent = '请选择文件';
                document.getElementById('nextStep1').disabled = true;
            } else if (selectedDocument && !selectedFiles.find(f => f.id === selectedDocument.id)) {
                // 如果当前选中的文件被删除了，选择第一个文件
                selectDocument(selectedFiles[0]);
            }
        }
    </script>
</body>
</html>