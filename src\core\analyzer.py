"""
Core document analyzer class.
"""

import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

from .config import Config
from .prompts import PromptTemplates
from .llm_client import create_llm_client
from src.utils.document_reader import Document

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """Result of document analysis."""
    doc_id: str
    title: Optional[str] = None
    analysis: Dict[str, Any] = None
    quality_score: float = 0.0
    execution_time: float = 0.0
    confidence_scores: Dict[str, float] = None
    warnings: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.analysis is None:
            self.analysis = {}
        if self.confidence_scores is None:
            self.confidence_scores = {}
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}

class DocumentAnalyzer:
    """Main document analysis class."""
    
    def __init__(self, config: Config):
        self.config = config
        self.prompt_templates = PromptTemplates()
        
        # Setup LLM client
        self.llm_client = self._setup_llm_client()
        
        # Create directories
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Analysis history
        self.analysis_history: List[AnalysisResult] = []
    
    def _setup_llm_client(self):
        """Setup LLM client based on configuration."""
        return create_llm_client(self.config.llm, self.config.use_mock_llm)
    
    def analyze_document(self, document: Document, 
                       template_name: str = "unified_analysis") -> AnalysisResult:
        """Analyze a single document."""
        
        logger.info(f"Analyzing document: {document.doc_id} using template: {template_name}")
        
        start_time = time.time()
        
        try:
            # Validate document length
            if len(document.text) < 50:
                return AnalysisResult(
                    doc_id=document.doc_id,
                    title=document.title,
                    execution_time=time.time() - start_time,
                    warnings=["Document too short for analysis (minimum 50 characters required)"]
                )
            
            if len(document.text) > 50000:
                logger.warning(f"Document {document.doc_id} is very long ({len(document.text)} characters). Analysis may be truncated.")
            
            # Prepare variables for template
            variables = {
                'DOC_ID': document.doc_id,
                'TITLE': document.title or '',
                'DOCUMENT_TEXT': document.text,
                'KNOWN_ACTORS': ''
            }
            
            # Get formatted prompt
            prompt_text = self.prompt_templates.format_template(template_name, variables)
            
            # Log prompt length for debugging
            logger.debug(f"Prompt length for {document.doc_id}: {len(prompt_text)} characters")
            
            # Get LLM response with retry mechanism
            response_text = None
            max_retries = 2
            retry_count = 0
            
            while retry_count <= max_retries and response_text is None:
                try:
                    response_text = self.llm_client.generate_response(prompt_text)
                except Exception as e:
                    retry_count += 1
                    if retry_count <= max_retries:
                        logger.warning(f"Retry {retry_count}/{max_retries} for document {document.doc_id}: {e}")
                        time.sleep(1 * retry_count)  # Exponential backoff
                    else:
                        logger.error(f"Failed to get LLM response after {max_retries} retries: {e}")
                        raise
            
            # Parse response
            parsed_result = None
            parse_success = False
            
            try:
                parsed_result = json.loads(response_text)
                parse_success = True
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                # Try to extract JSON from response
                parsed_result = self._extract_json_from_text(response_text)
                if parsed_result:
                    parse_success = True
                else:
                    logger.error(f"Could not extract valid JSON from response for document {document.doc_id}")
                    logger.debug(f"Raw response: {response_text[:500]}...")
            
            execution_time = time.time() - start_time
            
            if not parse_success:
                return AnalysisResult(
                    doc_id=document.doc_id,
                    title=document.title,
                    execution_time=execution_time,
                    warnings=["Failed to parse LLM response as JSON"]
                )
            
            # Process and validate results based on template type
            if template_name == "quick_summary":
                # For quick summary, use the result directly
                processed_result = parsed_result
                quality_score = 0.8  # Default good score for quick summary
                confidence_scores = {"summary": 0.8}
                warnings = []
            else:
                # For other templates, use the standard processing
                processed_result = self._process_analysis_result(parsed_result, document)
                
                # Calculate quality score
                quality_score = self._calculate_quality_score(processed_result)
                
                # Calculate confidence scores
                confidence_scores = self._calculate_confidence_scores(processed_result)
                
                # Generate warnings
                warnings = self._generate_warnings(processed_result)
            
            result = AnalysisResult(
                doc_id=document.doc_id,
                title=document.title,
                analysis=processed_result,
                quality_score=quality_score,
                execution_time=execution_time,
                confidence_scores=confidence_scores,
                warnings=warnings,
                metadata={
                    'template_used': template_name,
                    'document_length': len(document.text),
                    'parse_success': parse_success,
                    'timestamp': time.time()
                }
            )
            
            self.analysis_history.append(result)
            
            logger.info(f"Analysis completed for {document.doc_id} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error analyzing document {document.doc_id}: {e}")
            
            return AnalysisResult(
                doc_id=document.doc_id,
                title=document.title,
                execution_time=execution_time,
                warnings=[f"Analysis failed: {str(e)}"]
            )
    
    def analyze_with_multiple_templates(self, document: Document, 
                                     template_names: List[str]) -> Dict[str, AnalysisResult]:
        """Analyze document with multiple templates."""
        
        results = {}
        
        for template_name in template_names:
            try:
                result = self.analyze_document(document, template_name)
                results[template_name] = result
            except Exception as e:
                logger.error(f"Error with template {template_name}: {e}")
                continue
        
        return results
    
    def analyze_documents_batch(self, documents: List[Document], 
                             template_name: str = "unified_analysis") -> List[AnalysisResult]:
        """Analyze multiple documents in batch."""
        
        results = []
        
        for document in documents:
            try:
                result = self.analyze_document(document, template_name)
                results.append(result)
            except Exception as e:
                logger.error(f"Error analyzing document {document.doc_id}: {e}")
                continue
        
        return results
    
    def _extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from text that might contain other content."""
        try:
            import re
            import json
            
            # Clean up common formatting issues
            cleaned_text = text.strip()
            
            # Remove markdown code block markers
            cleaned_text = re.sub(r'```json\s*', '', cleaned_text)
            cleaned_text = re.sub(r'```\s*$', '', cleaned_text)
            
            # First, try to parse the entire text as JSON (with fixes)
            try:
                parsed = self._fix_and_parse_json(cleaned_text)
                if parsed:
                    return parsed
            except:
                pass
            
            # If that fails, try to find JSON object patterns
            json_patterns = [
                r'\{[\s\S]*?\}',  # JSON object
                r'\[[\s\S]*?\]',  # JSON array
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, cleaned_text, re.DOTALL)
                for match in matches:
                    try:
                        # Try to parse the match as JSON with fixes
                        parsed = self._fix_and_parse_json(match)
                        if parsed:
                            return parsed
                    except:
                        continue
            
            # If still no valid JSON found, return None
            return None
            
        except Exception:
            return None
    
    def _fix_and_parse_json(self, text: str) -> Optional[Dict[str, Any]]:
        """Try to fix common JSON issues and parse."""
        try:
            import json
            import re
            
            # Common fixes
            fixed_text = text.strip()
            
            # Fix trailing commas before closing brackets/braces
            fixed_text = re.sub(r',(\s*[}\]])', r'\1', fixed_text)
            
            # Fix missing quotes around keys (more careful pattern)
            fixed_text = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', fixed_text)
            
            # Fix single quotes around values (but preserve single quotes within strings)
            # This is more complex - we need to be careful about escaped quotes
            fixed_text = re.sub(r"'([^']*?)'", r'"\1"', fixed_text)
            
            # Fix single quotes around keys
            fixed_text = re.sub(r'([{,]\s*)\'([a-zA-Z_][a-zA-Z0-9_]*)\'\s*:', r'\1"\2":', fixed_text)
            
            # Try to parse the fixed text
            try:
                parsed = json.loads(fixed_text)
                if isinstance(parsed, dict):
                    return parsed
            except json.JSONDecodeError as e:
                # If still failing, try more aggressive fixes
                pass
                
            # More aggressive fix for mixed quotes
            try:
                # Replace all single quotes with double quotes for keys
                fixed_text2 = re.sub(r'([{,]\s*)\'([a-zA-Z_][a-zA-Z0-9_]*)\'\s*:', r'\1"\2":', fixed_text)
                # Replace all single quotes with double quotes for values
                fixed_text2 = re.sub(r':\s*\'([^\']*?)\'', r': "\1"', fixed_text2)
                parsed = json.loads(fixed_text2)
                if isinstance(parsed, dict):
                    return parsed
            except json.JSONDecodeError:
                pass
                
        except Exception:
            pass
            
        return None
    
    def _process_analysis_result(self, result: Dict[str, Any], document: Document) -> Dict[str, Any]:
        """Process and validate analysis result."""
        
        processed = {}
        
        # Add document metadata
        processed['doc_id'] = result.get('doc_id', document.doc_id)
        processed['title'] = result.get('title', document.title)
        
        # Process actors
        if 'actors' in result:
            processed['actors'] = self._process_actors(result['actors'])
        
        # Process relationships
        if 'relationships' in result:
            processed['relationships'] = self._process_relationships(result['relationships'])
        
        # Process portrayals
        if 'portrayals' in result:
            processed['portrayals'] = self._process_portrayals(result['portrayals'])
        
        # Process issue scope
        if 'issue_scope' in result:
            processed['issue_scope'] = self._process_issue_scope(result['issue_scope'])
        
        # Process causal mechanisms
        if 'causal_mechanisms' in result:
            processed['causal_mechanisms'] = self._process_causal_mechanisms(result['causal_mechanisms'])
        
        # Process AI decisions
        if 'ai_decisions' in result:
            processed['ai_decisions'] = result['ai_decisions']
        
        return processed
    
    def _process_actors(self, actors: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process actors data."""
        processed_actors = []
        
        for actor in actors:
            if isinstance(actor, dict):
                processed_actor = {
                    'name': actor.get('name', ''),
                    'stakeholder_category': actor.get('stakeholder_category', 'Others'),
                    'notes': actor.get('notes')
                }
                
                # Validate category
                if processed_actor['stakeholder_category'] not in self.config.analysis.stakeholder_categories:
                    processed_actor['stakeholder_category'] = 'Others'
                
                processed_actors.append(processed_actor)
        
        return processed_actors
    
    def _process_relationships(self, relationships: Dict[str, Any]) -> Dict[str, Any]:
        """Process relationships data."""
        if not isinstance(relationships, dict):
            return {'skipped': True, 'skip_reason': 'Invalid format', 'items': []}
        
        processed = {
            'skipped': relationships.get('skipped', False),
            'skip_reason': relationships.get('skip_reason'),
            'items': []
        }
        
        items = relationships.get('items', [])
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    processed_item = {
                        'a_name': item.get('a_name', ''),
                        'b_name': item.get('b_name', ''),
                        'relationship': item.get('relationship', ''),
                        'a_character': item.get('a_character'),
                        'b_character': item.get('b_character'),
                        'a_type': item.get('a_type', 'Others'),
                        'b_type': item.get('b_type', 'Others'),
                        'evidence': item.get('evidence', '')
                    }
                    processed['items'].append(processed_item)
        
        return processed
    
    def _process_portrayals(self, portrayals: Dict[str, Any]) -> Dict[str, Any]:
        """Process portrayals data."""
        if not isinstance(portrayals, dict):
            return {'skipped': True, 'skip_reason': 'Invalid format', 'items': [], 'shifts': {}}
        
        processed = {
            'skipped': portrayals.get('skipped', False),
            'skip_reason': portrayals.get('skip_reason'),
            'items': [],
            'shifts': {}
        }
        
        # Process items
        items = portrayals.get('items', [])
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    processed_item = {
                        'actor': item.get('actor', ''),
                        'type': item.get('type', ''),
                        'evidence': item.get('evidence', ''),
                        'explanation': item.get('explanation', '')
                    }
                    processed['items'].append(processed_item)
        
        # Process shifts
        shifts = portrayals.get('shifts', {})
        if isinstance(shifts, dict):
            processed['shifts'] = {
                'angel_shift_present': shifts.get('angel_shift_present', False),
                'devil_shift_present': shifts.get('devil_shift_present', False),
                'angel_shift_evidence': shifts.get('angel_shift_evidence'),
                'devil_shift_evidence': shifts.get('devil_shift_evidence')
            }
        
        return processed
    
    def _process_issue_scope(self, issue_scope: Dict[str, Any]) -> Dict[str, Any]:
        """Process issue scope data."""
        if not isinstance(issue_scope, dict):
            return {'skipped': True, 'skip_reason': 'Invalid format', 'items': []}
        
        processed = {
            'skipped': issue_scope.get('skipped', False),
            'skip_reason': issue_scope.get('skip_reason'),
            'items': []
        }
        
        items = issue_scope.get('items', [])
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    processed_item = {
                        'actor': item.get('actor', ''),
                        'type': item.get('type', ''),
                        'evidence': item.get('evidence', ''),
                        'explanation': item.get('explanation', '')
                    }
                    processed['items'].append(processed_item)
        
        return processed
    
    def _process_causal_mechanisms(self, causal_mechanisms: Dict[str, Any]) -> Dict[str, Any]:
        """Process causal mechanisms data."""
        if not isinstance(causal_mechanisms, dict):
            return {'skipped': True, 'skip_reason': 'Invalid format', 'items': []}
        
        processed = {
            'skipped': causal_mechanisms.get('skipped', False),
            'skip_reason': causal_mechanisms.get('skip_reason'),
            'items': []
        }
        
        items = causal_mechanisms.get('items', [])
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    processed_item = {
                        'actor': item.get('actor', ''),
                        'type': item.get('type', ''),
                        'evidence': item.get('evidence', ''),
                        'explanation': item.get('explanation', '')
                    }
                    processed['items'].append(processed_item)
        
        return processed
    
    def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """Calculate quality score for analysis result."""
        score = 0.0
        max_score = 0.0
        
        # Check actors (30%)
        max_score += 0.3
        actors = result.get('actors', [])
        if actors and len(actors) > 0:
            actor_score = 0.0
            for actor in actors:
                # Base score for having an actor
                actor_score += 0.05
                # Bonus for having stakeholder category
                if actor.get('stakeholder_category') and actor['stakeholder_category'] != 'Others':
                    actor_score += 0.03
                # Bonus for having notes
                if actor.get('notes'):
                    actor_score += 0.02
            score += min(0.3, actor_score)
        
        # Check relationships (20%)
        max_score += 0.2
        relationships = result.get('relationships', {})
        if relationships and not relationships.get('skipped', True):
            items = relationships.get('items', [])
            if items and len(items) > 0:
                relationship_score = 0.0
                for item in items:
                    # Base score for having a relationship
                    relationship_score += 0.05
                    # Bonus for having both actors defined
                    if item.get('a_name') and item.get('b_name'):
                        relationship_score += 0.02
                    # Bonus for having evidence
                    if item.get('evidence') and len(item['evidence']) > 10:
                        relationship_score += 0.03
                score += min(0.2, relationship_score)
        
        # Check portrayals (20%)
        max_score += 0.2
        portrayals = result.get('portrayals', {})
        if portrayals and not portrayals.get('skipped', True):
            items = portrayals.get('items', [])
            if items and len(items) > 0:
                portrayal_score = 0.0
                for item in items:
                    # Base score for having a portrayal
                    portrayal_score += 0.05
                    # Bonus for having evidence
                    if item.get('evidence') and len(item['evidence']) > 10:
                        portrayal_score += 0.03
                    # Bonus for having explanation
                    if item.get('explanation') and len(item['explanation']) > 20:
                        portrayal_score += 0.02
                score += min(0.2, portrayal_score)
            
            # Bonus for detecting shifts
            shifts = portrayals.get('shifts', {})
            if shifts.get('angel_shift_present') or shifts.get('devil_shift_present'):
                score += 0.05
        
        # Check issue scope (15%)
        max_score += 0.15
        issue_scope = result.get('issue_scope', {})
        if issue_scope and not issue_scope.get('skipped', True):
            items = issue_scope.get('items', [])
            if items and len(items) > 0:
                scope_score = 0.0
                for item in items:
                    # Base score for having a scope item
                    scope_score += 0.05
                    # Bonus for having evidence
                    if item.get('evidence') and len(item['evidence']) > 10:
                        scope_score += 0.03
                    # Bonus for having explanation
                    if item.get('explanation') and len(item['explanation']) > 20:
                        scope_score += 0.02
                score += min(0.15, scope_score)
        
        # Check causal mechanisms (15%)
        max_score += 0.15
        causal_mechanisms = result.get('causal_mechanisms', {})
        if causal_mechanisms and not causal_mechanisms.get('skipped', True):
            items = causal_mechanisms.get('items', [])
            if items and len(items) > 0:
                causal_score = 0.0
                for item in items:
                    # Base score for having a causal mechanism
                    causal_score += 0.05
                    # Bonus for having evidence
                    if item.get('evidence') and len(item['evidence']) > 10:
                        causal_score += 0.03
                    # Bonus for having explanation
                    if item.get('explanation') and len(item['explanation']) > 20:
                        causal_score += 0.02
                score += min(0.15, causal_score)
        
        # Apply penalty for completely empty results
        if score == 0.0:
            return 0.0
        
        # Normalize score
        normalized_score = score / max_score if max_score > 0 else 0.0
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, normalized_score))
    
    def _calculate_confidence_scores(self, result: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for different analysis components."""
        scores = {}
        
        # Actor confidence
        actors = result.get('actors', [])
        scores['actors'] = min(1.0, len(actors) * 0.2) if actors else 0.0
        
        # Relationship confidence
        relationships = result.get('relationships', {})
        if relationships and not relationships.get('skipped', True):
            items = relationships.get('items', [])
            scores['relationships'] = min(1.0, len(items) * 0.3) if items else 0.0
        else:
            scores['relationships'] = 0.0
        
        # Portrayal confidence
        portrayals = result.get('portrayals', {})
        if portrayals and not portrayals.get('skipped', True):
            items = portrayals.get('items', [])
            scores['portrayals'] = min(1.0, len(items) * 0.3) if items else 0.0
        else:
            scores['portrayals'] = 0.0
        
        # Issue scope confidence
        issue_scope = result.get('issue_scope', {})
        if issue_scope and not issue_scope.get('skipped', True):
            items = issue_scope.get('items', [])
            scores['issue_scope'] = min(1.0, len(items) * 0.4) if items else 0.0
        else:
            scores['issue_scope'] = 0.0
        
        # Causal mechanisms confidence
        causal_mechanisms = result.get('causal_mechanisms', {})
        if causal_mechanisms and not causal_mechanisms.get('skipped', True):
            items = causal_mechanisms.get('items', [])
            scores['causal_mechanisms'] = min(1.0, len(items) * 0.4) if items else 0.0
        else:
            scores['causal_mechanisms'] = 0.0
        
        return scores
    
    def _generate_warnings(self, result: Dict[str, Any]) -> List[str]:
        """Generate warnings based on analysis results."""
        warnings = []
        
        # Check for empty results
        actors = result.get('actors', [])
        if not actors:
            warnings.append("No actors found in the analysis")
        
        # Check for skipped sections
        for section in ['relationships', 'portrayals', 'issue_scope', 'causal_mechanisms']:
            section_data = result.get(section, {})
            if section_data.get('skipped', False):
                reason = section_data.get('skip_reason', 'Unknown reason')
                warnings.append(f"{section.replace('_', ' ').title()} analysis skipped: {reason}")
        
        # Check for low confidence
        confidence_scores = self._calculate_confidence_scores(result)
        for component, score in confidence_scores.items():
            if score < 0.3:
                warnings.append(f"Low confidence in {component.replace('_', ' ')} analysis")
        
        return warnings
    
    def save_result(self, result: AnalysisResult, filename: Optional[str] = None) -> str:
        """Save analysis result to file."""
        if filename is None:
            filename = f"analysis_{result.doc_id}_{int(time.time())}.json"
        
        output_file = self.output_dir / filename
        
        result_data = asdict(result)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved analysis result to {output_file}")
        return str(output_file)
    
    def save_batch_results(self, results: List[AnalysisResult], filename: Optional[str] = None) -> str:
        """Save batch analysis results to file."""
        if filename is None:
            timestamp = int(time.time())
            filename = f"batch_analysis_{timestamp}.json"
        
        output_file = self.output_dir / filename
        
        results_data = {
            'metadata': {
                'timestamp': time.time(),
                'total_documents': len(results),
                'config': self.config.to_dict()
            },
            'results': [asdict(result) for result in results]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved batch results to {output_file}")
        return str(output_file)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get summary of all analyses performed."""
        if not self.analysis_history:
            return {}
        
        total_analyses = len(self.analysis_history)
        successful_analyses = sum(1 for r in self.analysis_history if r.analysis)
        failed_analyses = total_analyses - successful_analyses
        
        avg_quality_score = sum(r.quality_score for r in self.analysis_history) / total_analyses
        avg_execution_time = sum(r.execution_time for r in self.analysis_history) / total_analyses
        
        return {
            'total_analyses': total_analyses,
            'successful_analyses': successful_analyses,
            'failed_analyses': failed_analyses,
            'success_rate': successful_analyses / total_analyses,
            'average_quality_score': avg_quality_score,
            'average_execution_time': avg_execution_time,
            'templates_used': list(set(r.metadata.get('template_used', 'unknown') for r in self.analysis_history))
        }