#!/usr/bin/env python3
"""
Test script to check configuration loading in the web server context
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("=== Environment Variables ===")
print(f"ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
print(f"LLM_PROVIDER: {os.getenv('LLM_PROVIDER')}")
print(f"USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM')}")
print("==========================")

# Import and test the config
from src.core.config import Config

config = Config()
print("=== Config Object ===")
print(f"Provider: {config.llm.provider}")
print(f"Model: {config.llm.model}")
print(f"API Key: {'SET' if config.llm.api_key else 'NOT SET'}")
print(f"Use Mock: {config.use_mock_llm}")
print("====================")

# Test the web API config loading
import web_api
print("=== Web API Config ===")
web_api.initialize_app()
print(f"Provider: {web_api.config.llm.provider}")
print(f"Model: {web_api.config.llm.model}")
print(f"API Key: {'SET' if web_api.config.llm.api_key else 'NOT SET'}")
print(f"Use Mock: {web_api.config.use_mock_llm}")
print("====================")