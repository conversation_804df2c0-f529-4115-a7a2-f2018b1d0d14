#!/usr/bin/env python3
"""
Test script for ZhipuAI API integration.
"""

import os
import sys
import json
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import Config
from core.llm_client import create_llm_client
from utils.document_reader import create_sample_document


def test_zhipuai_integration():
    """Test ZhipuAI API integration."""
    print("Testing ZhipuAI API Integration...")
    print("=" * 50)
    
    # Load configuration
    config = Config()
    
    # Override with environment variables
    if os.getenv('ZHIPUAI_API_KEY'):
        config.llm.api_key = os.getenv('ZHIPUAI_API_KEY')
    if os.getenv('LLM_PROVIDER'):
        config.llm.provider = os.getenv('LLM_PROVIDER')
    if os.getenv('LLM_MODEL'):
        config.llm.model = os.getenv('LLM_MODEL')
    if os.getenv('USE_MOCK_LLM'):
        config.use_mock_llm = os.getenv('USE_MOCK_LLM').lower() == 'true'
    
    print(f"Provider: {config.llm.provider}")
    print(f"Model: {config.llm.model}")
    print(f"API Key: {'***' + config.llm.api_key[-4:] if config.llm.api_key else 'None'}")
    print(f"Use Mock: {config.use_mock_llm}")
    print()
    
    # Check if we have API key
    if not config.use_mock_llm and not config.llm.api_key:
        print("ERROR: No API key found. Please set ZHIPUAI_API_KEY environment variable.")
        return False
    
    try:
        # Create LLM client
        print("Creating LLM client...")
        llm_client = create_llm_client(config.llm, config.use_mock_llm)
        print("SUCCESS: LLM client created successfully")
        print()
        
        # Test with a simple prompt
        print("Testing with simple prompt...")
        test_prompt = "请用中文回答：什么是人工智能？"
        print(f"Prompt: {test_prompt}")
        
        response = llm_client.generate_response(test_prompt)
        print(f"Response: {response}")
        print("SUCCESS: Simple prompt test successful")
        print()
        
        # Test with document analysis
        print("Testing with document analysis...")
        document = create_sample_document()
        
        analysis_prompt = f"""
        请分析以下文档，提取关键信息并以JSON格式返回：
        
        文档标题: {document.title}
        文档内容: {document.text[:500]}...
        
        请返回包含以下字段的JSON：
        - title: 文档标题
        - summary: 文档摘要
        - key_points: 关键点列表
        - sentiment: 情感分析
        """
        
        response = llm_client.generate_response(analysis_prompt)
        print("Document analysis response:")
        print(response)
        print("SUCCESS: Document analysis test successful")
        print()
        
        # Try to parse as JSON
        try:
            parsed = json.loads(response)
            print("SUCCESS: Response is valid JSON")
            print(f"Keys: {list(parsed.keys())}")
        except json.JSONDecodeError:
            print("WARNING: Response is not valid JSON")
        
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False


def main():
    """Main function."""
    print("ZhipuAI API Integration Test")
    print("=" * 50)
    
    # Check environment variables
    print("Environment Variables:")
    print(f"ZHIPUAI_API_KEY: {'***' + os.getenv('ZHIPUAI_API_KEY', '')[-4:] if os.getenv('ZHIPUAI_API_KEY') else 'Not set'}")
    print(f"LLM_PROVIDER: {os.getenv('LLM_PROVIDER', 'Not set')}")
    print(f"LLM_MODEL: {os.getenv('LLM_MODEL', 'Not set')}")
    print(f"USE_MOCK_LLM: {os.getenv('USE_MOCK_LLM', 'Not set')}")
    print()
    
    # Run test
    success = test_zhipuai_integration()
    
    if success:
        print("\nSUCCESS: All tests passed!")
    else:
        print("\nFAILED: Tests failed!")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())