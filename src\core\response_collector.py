"""
LLM Response Collector Module.

This module provides functionality to collect, store, and manage LLM response data
for visualization and statistical analysis.
"""

import os
import json
import time
import hashlib
import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import logging

logger = logging.getLogger(__name__)

class ResponseCollector:
    """
    Collects and stores LLM response data for later analysis and visualization.
    """
    
    def __init__(self, storage_dir: str = "results/llm_responses"):
        """
        Initialize the response collector.
        
        Args:
            storage_dir: Directory where response data will be stored
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # Ensure we can write to the directory
        if not os.access(self.storage_dir, os.W_OK):
            logger.warning(f"No write permission for {self.storage_dir}, using temp directory")
            import tempfile
            self.storage_dir = Path(tempfile.gettempdir()) / "llm_responses"
            self.storage_dir.mkdir(parents=True, exist_ok=True)
            
        logger.info(f"Response collector initialized. Storage directory: {self.storage_dir}")
        
    def _generate_response_id(self, response_data: Dict[str, Any]) -> str:
        """
        Generate a unique ID for a response based on its content.
        
        Args:
            response_data: The response data dictionary
            
        Returns:
            A unique ID string
        """
        # Create a hash from core response properties
        content_to_hash = []
        
        # Try to use document_id if available
        if 'document_id' in response_data:
            content_to_hash.append(str(response_data['document_id']))
        
        # Use task list if available
        if 'tasks' in response_data:
            if isinstance(response_data['tasks'], list):
                content_to_hash.append(','.join(sorted(response_data['tasks'])))
            else:
                content_to_hash.append(str(response_data['tasks']))
        
        # Add mode if available
        if 'mode' in response_data:
            content_to_hash.append(str(response_data['mode']))
            
        # Add language if available
        if 'language' in response_data:
            content_to_hash.append(str(response_data['language']))
            
        # Add timestamp
        content_to_hash.append(str(time.time()))
        
        # Generate hash
        content_str = '|'.join(content_to_hash)
        return hashlib.md5(content_str.encode('utf-8')).hexdigest()
        
    def save_response(self, response_data: Dict[str, Any]) -> str:
        """
        Save an LLM response to storage.
        
        Args:
            response_data: The response data to save
            
        Returns:
            Path where the response was saved
        """
        # Generate response ID and filename
        response_id = self._generate_response_id(response_data)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"response_{response_id}_{timestamp}.json"
        file_path = self.storage_dir / filename
        
        # Add metadata
        response_data['_collector_metadata'] = {
            'saved_at': datetime.datetime.now().isoformat(),
            'response_id': response_id
        }
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Saved response {response_id} to {file_path}")
        return str(file_path)
    
    def save_batch_responses(self, responses: List[Dict[str, Any]]) -> List[str]:
        """
        Save multiple LLM responses.
        
        Args:
            responses: List of response data dictionaries
            
        Returns:
            List of paths where responses were saved
        """
        saved_paths = []
        for response in responses:
            path = self.save_response(response)
            saved_paths.append(path)
        return saved_paths
    
    def list_responses(self, pattern: str = "*.json") -> List[Path]:
        """
        List available response files.
        
        Args:
            pattern: File pattern to match
            
        Returns:
            List of file paths
        """
        return list(self.storage_dir.glob(pattern))
    
    def clear_old_responses(self, days_old: int = 30) -> int:
        """
        Delete responses older than specified days.
        
        Args:
            days_old: Age threshold in days
            
        Returns:
            Number of files deleted
        """
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        count = 0
        
        for file_path in self.storage_dir.glob("*.json"):
            if file_path.stat().st_mtime < cutoff_time:
                file_path.unlink()
                count += 1
                
        logger.info(f"Deleted {count} response files older than {days_old} days")
        return count
