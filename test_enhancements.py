"""
Test script to verify system enhancements.
"""
import os
import json
import time
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import components
from src.core.config import Config
from src.core.modular_analyzer import ModularDocumentAnalyzer
from src.core.modular_prompts import TaskType, AnalysisMode
from src.core.cache_manager import CacheManager
from src.core.api_monitor import APIMonitor

# Initialize configuration
config = Config()

def test_modular_analyzer_with_cache():
    """Test the modular analyzer with caching."""
    print("\n===== TESTING MODULAR ANALYZER WITH CACHE =====")
    
    # Create test document
    doc_id = "cache_test_001"
    title = "Test Document for Cache"
    text = """
    In a surprising move last month, Shell announced it would withdraw from offshore wind projects,
    citing concerns about economic viability. Greenpeace criticized the decision, calling it
    "environmental negligence" and demanded the United Nations intervene with stricter regulations.
    """
    
    # Clear existing cache
    cache_dir = Path("cache/modular")
    if cache_dir.exists():
        for file in cache_dir.glob("*.json"):
            file.unlink()
    
    # Create analyzer with cache
    analyzer = ModularDocumentAnalyzer(config, use_cache=True)
    
    # Create document object for analysis
    class Document:
        def __init__(self, doc_id, text, title=None):
            self.doc_id = doc_id
            self.text = text
            self.title = title
    
    doc = Document(doc_id, text, title)
    
    # First run - should be cached
    print("First analysis run (no cache)...")
    start_time = time.time()
    result1 = analyzer.analyze_modular(
        document=doc,
        mode=AnalysisMode.STEPWISE,
        tasks=[TaskType.ACTORS_RELATIONSHIPS],
        language="en"
    )
    duration1 = time.time() - start_time
    
    # Check if cache files were created
    cache_files = list(cache_dir.glob("*.json"))
    print(f"Cache files created: {len(cache_files)}")
    
    # Second run - should use cache
    print("Second analysis run (should use cache)...")
    start_time = time.time()
    result2 = analyzer.analyze_modular(
        document=doc,
        mode=AnalysisMode.STEPWISE,
        tasks=[TaskType.ACTORS_RELATIONSHIPS],
        language="en"
    )
    duration2 = time.time() - start_time
    
    # Check cache performance
    cache_speedup = duration1 / duration2 if duration2 > 0 else float('inf')
    print(f"First run time: {duration1:.2f}s")
    print(f"Second run time: {duration2:.2f}s")
    print(f"Cache speedup: {cache_speedup:.2f}x")
    print(f"Quality scores - First: {result1.quality_score:.2f}, Second: {result2.quality_score:.2f}")
    
    # Check quality warnings
    print("\nQuality warnings:")
    for warning in result1.warnings:
        print(f"- {warning}")
    
    # Check if results match
    results_match = result1.task_results == result2.task_results
    print(f"Results match: {results_match}")
    
    return results_match

def test_api_monitor():
    """Test the API monitor functionality."""
    print("\n===== TESTING API MONITOR =====")
    
    monitor = APIMonitor(window_minutes=5)
    
    # Simulate some API requests
    print("Simulating API requests...")
    endpoints = [
        "/analyze/document", 
        "/analyze/modular",
        "/analyze/modes",
        "/health"
    ]
    
    for i in range(20):
        endpoint = endpoints[i % len(endpoints)]
        # Simulate varying response times and occasional errors
        duration = 0.2 + (i % 5) * 0.3
        status = 200 if i % 7 != 0 else 500
        
        monitor.record_request(endpoint, duration, status)
        time.sleep(0.1)  # Small delay to spread requests
    
    # Get current stats
    stats = monitor.get_current_stats()
    print("\nAPI Monitor Statistics:")
    print(f"Total requests: {stats['total_requests']}")
    print(f"Average response time: {stats['avg_response_time']:.2f}s")
    print(f"Success rate: {stats['success_rate']*100:.1f}%")
    
    # Check endpoint stats
    print("\nEndpoint Statistics:")
    for endpoint, ep_stats in stats['endpoints'].items():
        print(f"- {endpoint}:")
        print(f"  Requests: {ep_stats['requests']}")
        print(f"  Avg response time: {ep_stats['avg_response_time']:.2f}s")
        print(f"  Success rate: {ep_stats['success_rate']*100:.1f}%")
    
    # Check alerts
    alerts = monitor.get_alerts()
    print("\nPerformance Alerts:")
    if alerts:
        for alert in alerts:
            print(f"- {alert}")
    else:
        print("No alerts detected")
    
    return True

def test_chinese_prompt():
    """Test the enhanced Chinese prompt."""
    print("\n===== TESTING CHINESE PROMPT =====")
    
    # Create test document
    doc_id = "zh_test_001"
    title = "中文测试文档"
    text = """
    上个月，壳牌公司出人意料地宣布将退出海上风电项目，理由是经济可行性存在问题。
    绿色和平组织批评这一决定，称其为"环境疏忽"，并要求联合国介入，实施更严格的监管。
    """
    
    # Create analyzer
    analyzer = ModularDocumentAnalyzer(config)
    
    # Run analysis with Chinese language
    print("Running analysis with Chinese prompt...")
    try:
        doc = Document(doc_id, text, title)
        result = analyzer.analyze_modular(
            document=doc,
            mode=AnalysisMode.STEPWISE,
            tasks=[TaskType.ACTORS_RELATIONSHIPS],
            language="zh"
        )
        
        print(f"Analysis succeeded with quality score: {result.quality_score:.2f}")
        
        # Check actors extracted
        actors = result.task_results.get(TaskType.ACTORS_RELATIONSHIPS, {}).get("actors", [])
        print(f"Found {len(actors)} actors:")
        for actor in actors:
            print(f"- {actor.get('name')} ({actor.get('stakeholder_category')})")
            
        return True
    except Exception as e:
        print(f"Analysis failed: {e}")
        return False

if __name__ == "__main__":
    print("===== TESTING SYSTEM ENHANCEMENTS =====")
    
    # Set mock mode for testing
    os.environ["USE_MOCK_LLM"] = "TRUE"
    print("Running tests with mock LLM responses")
    
    # Create cache directory if it doesn't exist
    os.makedirs("cache/modular", exist_ok=True)
    os.makedirs("logs/api_stats", exist_ok=True)
    
    # Test enhanced quality scoring and warnings
    cache_test_success = test_modular_analyzer_with_cache()
    monitor_test_success = test_api_monitor()
    chinese_test_success = test_chinese_prompt()
    
    print("\n===== TEST RESULTS =====")
    print(f"Cache Test: {'PASSED' if cache_test_success else 'FAILED'}")
    print(f"Monitor Test: {'PASSED' if monitor_test_success else 'FAILED'}")
    print(f"Chinese Prompt Test: {'PASSED' if chinese_test_success else 'FAILED'}")
    
    if cache_test_success and monitor_test_success and chinese_test_success:
        print("\nAll enhancements are working correctly!")
    else:
        print("\nSome tests failed. Check the logs for details.")
        
    # Reset environment
    os.environ.pop("USE_MOCK_LLM", None)
