"""
LLM Client Factory implementation.
"""
import os
import logging
from typing import Optional

from .config import Config, LLMConfig
from .llm_client import BaseLLMClient, MockLLMClient, ZhipuAIClient, OpenAIClient

class LLMClientFactory:
    """Factory for creating LLM clients."""
    
    @staticmethod
    def create_client(config: Config) -> BaseLLMClient:
        """
        Create an LLM client based on configuration.
        
        Args:
            config: Configuration object
            
        Returns:
            An instance of appropriate LLM client
        """
        logger = logging.getLogger(__name__)
        
        # Check if mock mode is enabled
        use_mock = os.environ.get("USE_MOCK_LLM", "").lower() in ("true", "1", "yes")
        if use_mock:
            logger.info("Creating MockLLMClient")
            return MockLLMClient(LLMConfig())
        
        # Get provider from environment or config
        provider = os.environ.get("LLM_PROVIDER", "").lower()
        if not provider:
            # Use default provider
            provider = "zhipuai"
        
        # Create LLMConfig with default values
        llm_config = LLMConfig()
        
        # Set provider from environment
        llm_config.provider = provider
        
        # Set API key from environment variables
        if provider == "zhipuai":
            llm_config.api_key = os.environ.get("ZHIPUAI_API_KEY")
            llm_config.model = os.environ.get("ZHIPUAI_MODEL", "glm-4")
            logger.info("Creating ZhipuAIClient")
            return ZhipuAIClient(llm_config)
        elif provider == "openai":
            llm_config.api_key = os.environ.get("OPENAI_API_KEY")
            llm_config.model = os.environ.get("OPENAI_MODEL", "gpt-4")
            logger.info("Creating OpenAIClient")
            return OpenAIClient(llm_config)
        else:
            logger.warning(f"Unknown provider: {provider}, using MockLLMClient")
            return MockLLMClient(llm_config)
